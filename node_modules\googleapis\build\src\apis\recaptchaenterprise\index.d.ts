/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { recaptchaenterprise_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof recaptchaenterprise_v1.Recaptchaenterprise;
};
export declare function recaptchaenterprise(version: 'v1'): recaptchaenterprise_v1.Recaptchaenterprise;
export declare function recaptchaenterprise(options: recaptchaenterprise_v1.Options): recaptchaenterprise_v1.Recaptchaenterprise;
declare const auth: AuthPlus;
export { auth };
export { recaptchaenterprise_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
