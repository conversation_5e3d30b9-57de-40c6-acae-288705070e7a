/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { siteVerification_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof siteVerification_v1.Siteverification;
};
export declare function siteVerification(version: 'v1'): siteVerification_v1.Siteverification;
export declare function siteVerification(options: siteVerification_v1.Options): siteVerification_v1.Siteverification;
declare const auth: AuthPlus;
export { auth };
export { siteVerification_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
