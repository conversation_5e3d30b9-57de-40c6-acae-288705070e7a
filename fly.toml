# fly.toml app configuration file generated for banking-notification on 2025-08-09T22:23:43+07:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'banking-notification'
primary_region = 'sin'

[build]

[env]
  NODE_ENV = 'production'
  PORT = '8080'

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = 'off'
  auto_start_machines = true
  min_machines_running = 1
  processes = ['app']

  [[http_service.checks]]
    interval = '30s'
    timeout = '15s'
    grace_period = '10s'
    method = 'GET'
    path = '/health'
    protocol = 'http'
    tls_skip_verify = false

[[vm]]
  size = 'shared-cpu-1x'
