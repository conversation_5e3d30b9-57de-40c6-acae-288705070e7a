app = "banking-notification"
primary_region = "sin"

[build]

[env]
  NODE_ENV = "production"
  PORT = "8080"

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

[[http_service.checks]]
  interval = "30s"
  grace_period = "10s"
  method = "GET"
  path = "/health"
  protocol = "http"
  timeout = "15s"
  tls_skip_verify = false

[[vm]]
  memory = "256mb"
  cpu_kind = "shared"
  cpus = 1
