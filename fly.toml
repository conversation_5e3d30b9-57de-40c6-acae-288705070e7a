app = "banking-notification"
primary_region = "sin"

[build]

[env]
  NODE_ENV = "production"
  PORT = "8080"

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

# <PERSON><PERSON><PERSON> hình để keep alive 24/7
[services]
  protocol = "tcp"
  internal_port = 8080

  [[services.ports]]
    port = 80
    handlers = ["http"]
    force_https = true

  [[services.ports]]
    port = 443
    handlers = ["tls", "http"]

  [services.concurrency]
    type = "connections"
    hard_limit = 25
    soft_limit = 20

[[http_service.checks]]
  interval = "30s"
  grace_period = "10s"
  method = "GET"
  path = "/health"
  protocol = "http"
  timeout = "15s"
  tls_skip_verify = false

[[vm]]
  memory = "256mb"
  cpu_kind = "shared"
  cpus = 1

# Cấu hình machine để keep alive
[machine]
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1
  max_machines_running = 1
