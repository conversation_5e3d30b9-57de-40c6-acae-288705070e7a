{"name": "banking-notification", "version": "1.0.0", "description": "Backend system to process Gmail API for banking transaction notifications", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["gmail", "banking", "notifications", "mongodb"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "googleapis": "^129.0.0", "cheerio": "^1.0.0-rc.12", "node-cron": "^3.0.3", "dotenv": "^16.3.1", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}