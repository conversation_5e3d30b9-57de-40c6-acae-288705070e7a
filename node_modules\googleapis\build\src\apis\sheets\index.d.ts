/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { sheets_v4 } from './v4';
export declare const VERSIONS: {
    v4: typeof sheets_v4.Sheets;
};
export declare function sheets(version: 'v4'): sheets_v4.Sheets;
export declare function sheets(options: sheets_v4.Options): sheets_v4.Sheets;
declare const auth: AuthPlus;
export { auth };
export { sheets_v4 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
