/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { streetviewpublish_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof streetviewpublish_v1.Streetviewpublish;
};
export declare function streetviewpublish(version: 'v1'): streetviewpublish_v1.Streetviewpublish;
export declare function streetviewpublish(options: streetviewpublish_v1.Options): streetviewpublish_v1.Streetviewpublish;
declare const auth: AuthPlus;
export { auth };
export { streetviewpublish_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
