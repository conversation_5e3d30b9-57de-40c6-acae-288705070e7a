// Script để xóa historyId trong database để test lần đầu chạy
require('dotenv').config();
const mongoose = require('mongoose');

async function clearHistory() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    
    console.log('Clearing gmail_history collection...');
    const result = await mongoose.connection.db.collection('gmail_history').deleteMany({});
    
    console.log(`Deleted ${result.deletedCount} history records`);
    
    await mongoose.connection.close();
    console.log('Done!');
  } catch (error) {
    console.error('Error:', error);
  }
}

clearHistory();
