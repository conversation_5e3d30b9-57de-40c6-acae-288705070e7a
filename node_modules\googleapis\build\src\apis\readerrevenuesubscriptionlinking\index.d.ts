/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { readerrevenuesubscriptionlinking_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof readerrevenuesubscriptionlinking_v1.Readerrevenuesubscriptionlinking;
};
export declare function readerrevenuesubscriptionlinking(version: 'v1'): readerrevenuesubscriptionlinking_v1.Readerrevenuesubscriptionlinking;
export declare function readerrevenuesubscriptionlinking(options: readerrevenuesubscriptionlinking_v1.Options): readerrevenuesubscriptionlinking_v1.Readerrevenuesubscriptionlinking;
declare const auth: AuthPlus;
export { auth };
export { readerrevenuesubscriptionlinking_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
