{"beforeHandshake": false, "cause": {"code": "ECONNRESET", "errno": -4077, "syscall": "read"}, "errorLabelSet": {}, "level": "error", "message": "Failed to create indexes: read ECONNRESET", "service": "banking-notification", "stack": "MongoNetworkError: read ECONNRESET\n    at Connection.onSocketError (D:\\Code\\BankingNoti\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connection.js:109:22)\n    at TLSSocket.emit (node:events:536:35)\n    at emitErrorNT (node:internal/streams/destroy:170:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:129:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)", "timestamp": "2025-08-08T20:01:41.250Z"}