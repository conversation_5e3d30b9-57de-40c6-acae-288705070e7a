/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { versionhistory_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof versionhistory_v1.Versionhistory;
};
export declare function versionhistory(version: 'v1'): versionhistory_v1.Versionhistory;
export declare function versionhistory(options: versionhistory_v1.Options): versionhistory_v1.Versionhistory;
declare const auth: AuthPlus;
export { auth };
export { versionhistory_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
