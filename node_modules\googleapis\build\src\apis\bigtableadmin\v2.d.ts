/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace bigtableadmin_v2 {
    export interface Options extends GlobalOptions {
        version: 'v2';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Cloud Bigtable Admin API
     *
     * Administer your Cloud Bigtable tables and instances.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const bigtableadmin = google.bigtableadmin('v2');
     * ```
     */
    export class Bigtableadmin {
        context: APIRequestContext;
        operations: Resource$Operations;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * A configuration object describing how Cloud Bigtable should treat traffic from a particular end user application.
     */
    export interface Schema$AppProfile {
        /**
         * Long form description of the use case for this AppProfile.
         */
        description?: string | null;
        /**
         * Strongly validated etag for optimistic concurrency control. Preserve the value returned from `GetAppProfile` when calling `UpdateAppProfile` to fail the request if there has been a modification in the mean time. The `update_mask` of the request need not include `etag` for this protection to apply. See [Wikipedia](https://en.wikipedia.org/wiki/HTTP_ETag) and [RFC 7232](https://tools.ietf.org/html/rfc7232#section-2.3) for more details.
         */
        etag?: string | null;
        /**
         * Use a multi-cluster routing policy.
         */
        multiClusterRoutingUseAny?: Schema$MultiClusterRoutingUseAny;
        /**
         * The unique name of the app profile. Values are of the form `projects/{project\}/instances/{instance\}/appProfiles/_a-zA-Z0-9*`.
         */
        name?: string | null;
        /**
         * This field has been deprecated in favor of `standard_isolation.priority`. If you set this field, `standard_isolation.priority` will be set instead. The priority of requests sent using this app profile.
         */
        priority?: string | null;
        /**
         * Use a single-cluster routing policy.
         */
        singleClusterRouting?: Schema$SingleClusterRouting;
        /**
         * The standard options used for isolating this app profile's traffic from other use cases.
         */
        standardIsolation?: Schema$StandardIsolation;
    }
    /**
     * Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { "audit_configs": [ { "service": "allServices", "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \}, { "log_type": "ADMIN_READ" \} ] \}, { "service": "sampleservice.googleapis.com", "audit_log_configs": [ { "log_type": "DATA_READ" \}, { "log_type": "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] \} ] \} ] \} For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.
     */
    export interface Schema$AuditConfig {
        /**
         * The configuration for logging of each type of permission.
         */
        auditLogConfigs?: Schema$AuditLogConfig[];
        /**
         * Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.
         */
        service?: string | null;
    }
    /**
     * Provides the configuration for logging a type of permissions. Example: { "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \} ] \} This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.
     */
    export interface Schema$AuditLogConfig {
        /**
         * Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.
         */
        exemptedMembers?: string[] | null;
        /**
         * The log type that this config enables.
         */
        logType?: string | null;
    }
    /**
     * Limits for the number of nodes a Cluster can autoscale up/down to.
     */
    export interface Schema$AutoscalingLimits {
        /**
         * Required. Maximum number of nodes to scale up to.
         */
        maxServeNodes?: number | null;
        /**
         * Required. Minimum number of nodes to scale down to.
         */
        minServeNodes?: number | null;
    }
    /**
     * The Autoscaling targets for a Cluster. These determine the recommended nodes.
     */
    export interface Schema$AutoscalingTargets {
        /**
         * The cpu utilization that the Autoscaler should be trying to achieve. This number is on a scale from 0 (no utilization) to 100 (total utilization), and is limited between 10 and 80, otherwise it will return INVALID_ARGUMENT error.
         */
        cpuUtilizationPercent?: number | null;
        /**
         * The storage utilization that the Autoscaler should be trying to achieve. This number is limited between 2560 (2.5TiB) and 5120 (5TiB) for a SSD cluster and between 8192 (8TiB) and 16384 (16TiB) for an HDD cluster, otherwise it will return INVALID_ARGUMENT error. If this value is set to 0, it will be treated as if it were set to the default value: 2560 for SSD, 8192 for HDD.
         */
        storageUtilizationGibPerNode?: number | null;
    }
    /**
     * A backup of a Cloud Bigtable table.
     */
    export interface Schema$Backup {
        /**
         * Output only. The encryption information for the backup.
         */
        encryptionInfo?: Schema$EncryptionInfo;
        /**
         * Output only. `end_time` is the time that the backup was finished. The row data in the backup will be no newer than this timestamp.
         */
        endTime?: string | null;
        /**
         * Required. The expiration time of the backup, with microseconds granularity that must be at least 6 hours and at most 90 days from the time the request is received. Once the `expire_time` has passed, Cloud Bigtable will delete the backup and free the resources used by the backup.
         */
        expireTime?: string | null;
        /**
         * A globally unique identifier for the backup which cannot be changed. Values are of the form `projects/{project\}/instances/{instance\}/clusters/{cluster\}/ backups/_a-zA-Z0-9*` The final segment of the name must be between 1 and 50 characters in length. The backup is stored in the cluster identified by the prefix of the backup name of the form `projects/{project\}/instances/{instance\}/clusters/{cluster\}`.
         */
        name?: string | null;
        /**
         * Output only. Size of the backup in bytes.
         */
        sizeBytes?: string | null;
        /**
         * Output only. Name of the backup from which this backup was copied. If a backup is not created by copying a backup, this field will be empty. Values are of the form: projects//instances//backups/.
         */
        sourceBackup?: string | null;
        /**
         * Required. Immutable. Name of the table from which this backup was created. This needs to be in the same instance as the backup. Values are of the form `projects/{project\}/instances/{instance\}/tables/{source_table\}`.
         */
        sourceTable?: string | null;
        /**
         * Output only. `start_time` is the time that the backup was started (i.e. approximately the time the CreateBackup request is received). The row data in this backup will be no older than this timestamp.
         */
        startTime?: string | null;
        /**
         * Output only. The current state of the backup.
         */
        state?: string | null;
    }
    /**
     * Information about a backup.
     */
    export interface Schema$BackupInfo {
        /**
         * Output only. Name of the backup.
         */
        backup?: string | null;
        /**
         * Output only. This time that the backup was finished. Row data in the backup will be no newer than this timestamp.
         */
        endTime?: string | null;
        /**
         * Output only. Name of the backup from which this backup was copied. If a backup is not created by copying a backup, this field will be empty. Values are of the form: projects//instances//backups/.
         */
        sourceBackup?: string | null;
        /**
         * Output only. Name of the table the backup was created from.
         */
        sourceTable?: string | null;
        /**
         * Output only. The time that the backup was started. Row data in the backup will be no older than this timestamp.
         */
        startTime?: string | null;
    }
    /**
     * Associates `members`, or principals, with a `role`.
     */
    export interface Schema$Binding {
        /**
         * The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        condition?: Schema$Expr;
        /**
         * Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid\}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid\}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid\}.svc.id.goog[{namespace\}/{kubernetes-sa\}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid\}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain\}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `deleted:user:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid\}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid\}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid\}` and the recovered group retains the role in the binding.
         */
        members?: string[] | null;
        /**
         * Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`.
         */
        role?: string | null;
    }
    /**
     * Change stream configuration.
     */
    export interface Schema$ChangeStreamConfig {
        /**
         * How long the change stream should be retained. Change stream data older than the retention period will not be returned when reading the change stream from the table. Values must be at least 1 day and at most 7 days, and will be truncated to microsecond granularity.
         */
        retentionPeriod?: string | null;
    }
    /**
     * Request message for google.bigtable.admin.v2.BigtableTableAdmin.CheckConsistency
     */
    export interface Schema$CheckConsistencyRequest {
        /**
         * Required. The token created using GenerateConsistencyToken for the Table.
         */
        consistencyToken?: string | null;
        /**
         * Checks that reads using an app profile with `StandardIsolation` can see all writes committed before the token was created, even if the read and write target different clusters.
         */
        standardReadRemoteWrites?: Schema$StandardReadRemoteWrites;
    }
    /**
     * Response message for google.bigtable.admin.v2.BigtableTableAdmin.CheckConsistency
     */
    export interface Schema$CheckConsistencyResponse {
        /**
         * True only if the token is consistent. A token is consistent if replication has caught up with the restrictions specified in the request.
         */
        consistent?: boolean | null;
    }
    /**
     * A resizable group of nodes in a particular cloud location, capable of serving all Tables in the parent Instance.
     */
    export interface Schema$Cluster {
        /**
         * Configuration for this cluster.
         */
        clusterConfig?: Schema$ClusterConfig;
        /**
         * Immutable. The type of storage used by this cluster to serve its parent instance's tables, unless explicitly overridden.
         */
        defaultStorageType?: string | null;
        /**
         * Immutable. The encryption configuration for CMEK-protected clusters.
         */
        encryptionConfig?: Schema$EncryptionConfig;
        /**
         * Immutable. The location where this cluster's nodes and storage reside. For best performance, clients should be located as close as possible to this cluster. Currently only zones are supported, so values should be of the form `projects/{project\}/locations/{zone\}`.
         */
        location?: string | null;
        /**
         * The unique name of the cluster. Values are of the form `projects/{project\}/instances/{instance\}/clusters/a-z*`.
         */
        name?: string | null;
        /**
         * The number of nodes in the cluster. If no value is set, Cloud Bigtable automatically allocates nodes based on your data footprint and optimized for 50% storage utilization.
         */
        serveNodes?: number | null;
        /**
         * Output only. The current state of the cluster.
         */
        state?: string | null;
    }
    /**
     * Autoscaling config for a cluster.
     */
    export interface Schema$ClusterAutoscalingConfig {
        /**
         * Required. Autoscaling limits for this cluster.
         */
        autoscalingLimits?: Schema$AutoscalingLimits;
        /**
         * Required. Autoscaling targets for this cluster.
         */
        autoscalingTargets?: Schema$AutoscalingTargets;
    }
    /**
     * Configuration for a cluster.
     */
    export interface Schema$ClusterConfig {
        /**
         * Autoscaling configuration for this cluster.
         */
        clusterAutoscalingConfig?: Schema$ClusterAutoscalingConfig;
    }
    /**
     * The state of a table's data in a particular cluster.
     */
    export interface Schema$ClusterState {
        /**
         * Output only. The encryption information for the table in this cluster. If the encryption key protecting this resource is customer managed, then its version can be rotated in Cloud Key Management Service (Cloud KMS). The primary version of the key and its status will be reflected here when changes propagate from Cloud KMS.
         */
        encryptionInfo?: Schema$EncryptionInfo[];
        /**
         * Output only. The state of replication for the table in this cluster.
         */
        replicationState?: string | null;
    }
    /**
     * A set of columns within a table which share a common configuration.
     */
    export interface Schema$ColumnFamily {
        /**
         * Garbage collection rule specified as a protobuf. Must serialize to at most 500 bytes. NOTE: Garbage collection executes opportunistically in the background, and so it's possible for reads to return a cell even if it matches the active GC expression for its family.
         */
        gcRule?: Schema$GcRule;
        /**
         * Output only. Only available with STATS_VIEW, this includes summary statistics about column family contents. For statistics over an entire table, see TableStats above.
         */
        stats?: Schema$ColumnFamilyStats;
    }
    /**
     * Approximate statistics related to a single column family within a table. This information may change rapidly, interpreting these values at a point in time may already preset out-of-date information. Everything below is approximate, unless otherwise specified.
     */
    export interface Schema$ColumnFamilyStats {
        /**
         * How many cells are present per column qualifier in this column family, averaged over all rows containing any column in the column family. e.g. For column family "family" in a table with 3 rows: * A row with 3 cells in "family:col" and 1 cell in "other:col" (3 cells / 1 column in "family") * A row with 1 cell in "family:col", 7 cells in "family:other_col", and 7 cells in "other:data" (8 cells / 2 columns in "family") * A row with 3 cells in "other:col" (0 columns in "family", "family" not present) would report (3 + 8 + 0)/(1 + 2 + 0) = 3.66 in this field.
         */
        averageCellsPerColumn?: number | null;
        /**
         * How many column qualifiers are present in this column family, averaged over all rows in the table. e.g. For column family "family" in a table with 3 rows: * A row with cells in "family:col" and "other:col" (1 column in "family") * A row with cells in "family:col", "family:other_col", and "other:data" (2 columns in "family") * A row with cells in "other:col" (0 columns in "family", "family" not present) would report (1 + 2 + 0)/3 = 1.5 in this field.
         */
        averageColumnsPerRow?: number | null;
        /**
         * How much space the data in the column family occupies. This is roughly how many bytes would be needed to read the contents of the entire column family (e.g. by streaming all contents out).
         */
        logicalDataBytes?: string | null;
    }
    /**
     * Metadata type for the google.longrunning.Operation returned by CopyBackup.
     */
    export interface Schema$CopyBackupMetadata {
        /**
         * The name of the backup being created through the copy operation. Values are of the form `projects//instances//clusters//backups/`.
         */
        name?: string | null;
        /**
         * The progress of the CopyBackup operation.
         */
        progress?: Schema$OperationProgress;
        /**
         * Information about the source backup that is being copied from.
         */
        sourceBackupInfo?: Schema$BackupInfo;
    }
    /**
     * The request for CopyBackup.
     */
    export interface Schema$CopyBackupRequest {
        /**
         * Required. The id of the new backup. The `backup_id` along with `parent` are combined as {parent\}/backups/{backup_id\} to create the full backup name, of the form: `projects/{project\}/instances/{instance\}/clusters/{cluster\}/backups/{backup_id\}`. This string must be between 1 and 50 characters in length and match the regex _a-zA-Z0-9*.
         */
        backupId?: string | null;
        /**
         * Required. Required. The expiration time of the copied backup with microsecond granularity that must be at least 6 hours and at most 30 days from the time the request is received. Once the `expire_time` has passed, Cloud Bigtable will delete the backup and free the resources used by the backup.
         */
        expireTime?: string | null;
        /**
         * Required. The source backup to be copied from. The source backup needs to be in READY state for it to be copied. Copying a copied backup is not allowed. Once CopyBackup is in progress, the source backup cannot be deleted or cleaned up on expiration until CopyBackup is finished. Values are of the form: `projects//instances//clusters//backups/`.
         */
        sourceBackup?: string | null;
    }
    /**
     * Metadata type for the operation returned by CreateBackup.
     */
    export interface Schema$CreateBackupMetadata {
        /**
         * If set, the time at which this operation finished or was cancelled.
         */
        endTime?: string | null;
        /**
         * The name of the backup being created.
         */
        name?: string | null;
        /**
         * The name of the table the backup is created from.
         */
        sourceTable?: string | null;
        /**
         * The time at which this operation started.
         */
        startTime?: string | null;
    }
    /**
     * The metadata for the Operation returned by CreateCluster.
     */
    export interface Schema$CreateClusterMetadata {
        /**
         * The time at which the operation failed or was completed successfully.
         */
        finishTime?: string | null;
        /**
         * The request that prompted the initiation of this CreateCluster operation.
         */
        originalRequest?: Schema$CreateClusterRequest;
        /**
         * The time at which the original request was received.
         */
        requestTime?: string | null;
        /**
         * Keys: the full `name` of each table that existed in the instance when CreateCluster was first called, i.e. `projects//instances//tables/`. Any table added to the instance by a later API call will be created in the new cluster by that API call, not this one. Values: information on how much of a table's data has been copied to the newly-created cluster so far.
         */
        tables?: {
            [key: string]: Schema$TableProgress;
        } | null;
    }
    /**
     * Request message for BigtableInstanceAdmin.CreateCluster.
     */
    export interface Schema$CreateClusterRequest {
        /**
         * Required. The cluster to be created. Fields marked `OutputOnly` must be left blank.
         */
        cluster?: Schema$Cluster;
        /**
         * Required. The ID to be used when referring to the new cluster within its instance, e.g., just `mycluster` rather than `projects/myproject/instances/myinstance/clusters/mycluster`.
         */
        clusterId?: string | null;
        /**
         * Required. The unique name of the instance in which to create the new cluster. Values are of the form `projects/{project\}/instances/{instance\}`.
         */
        parent?: string | null;
    }
    /**
     * The metadata for the Operation returned by CreateInstance.
     */
    export interface Schema$CreateInstanceMetadata {
        /**
         * The time at which the operation failed or was completed successfully.
         */
        finishTime?: string | null;
        /**
         * The request that prompted the initiation of this CreateInstance operation.
         */
        originalRequest?: Schema$CreateInstanceRequest;
        /**
         * The time at which the original request was received.
         */
        requestTime?: string | null;
    }
    /**
     * Request message for BigtableInstanceAdmin.CreateInstance.
     */
    export interface Schema$CreateInstanceRequest {
        /**
         * Required. The clusters to be created within the instance, mapped by desired cluster ID, e.g., just `mycluster` rather than `projects/myproject/instances/myinstance/clusters/mycluster`. Fields marked `OutputOnly` must be left blank.
         */
        clusters?: {
            [key: string]: Schema$Cluster;
        } | null;
        /**
         * Required. The instance to create. Fields marked `OutputOnly` must be left blank.
         */
        instance?: Schema$Instance;
        /**
         * Required. The ID to be used when referring to the new instance within its project, e.g., just `myinstance` rather than `projects/myproject/instances/myinstance`.
         */
        instanceId?: string | null;
        /**
         * Required. The unique name of the project in which to create the new instance. Values are of the form `projects/{project\}`.
         */
        parent?: string | null;
    }
    /**
     * Request message for google.bigtable.admin.v2.BigtableTableAdmin.CreateTable
     */
    export interface Schema$CreateTableRequest {
        /**
         * The optional list of row keys that will be used to initially split the table into several tablets (tablets are similar to HBase regions). Given two split keys, `s1` and `s2`, three tablets will be created, spanning the key ranges: `[, s1), [s1, s2), [s2, )`. Example: * Row keys := `["a", "apple", "custom", "customer_1", "customer_2",` `"other", "zz"]` * initial_split_keys := `["apple", "customer_1", "customer_2", "other"]` * Key assignment: - Tablet 1 `[, apple) =\> {"a"\}.` - Tablet 2 `[apple, customer_1) =\> {"apple", "custom"\}.` - Tablet 3 `[customer_1, customer_2) =\> {"customer_1"\}.` - Tablet 4 `[customer_2, other) =\> {"customer_2"\}.` - Tablet 5 `[other, ) =\> {"other", "zz"\}.`
         */
        initialSplits?: Schema$Split[];
        /**
         * Required. The Table to create.
         */
        table?: Schema$Table;
        /**
         * Required. The name by which the new table should be referred to within the parent instance, e.g., `foobar` rather than `{parent\}/tables/foobar`. Maximum 50 characters.
         */
        tableId?: string | null;
    }
    /**
     * Request message for google.bigtable.admin.v2.BigtableTableAdmin.DropRowRange
     */
    export interface Schema$DropRowRangeRequest {
        /**
         * Delete all rows in the table. Setting this to false is a no-op.
         */
        deleteAllDataFromTable?: boolean | null;
        /**
         * Delete all rows that start with this row key prefix. Prefix cannot be zero length.
         */
        rowKeyPrefix?: string | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * Cloud Key Management Service (Cloud KMS) settings for a CMEK-protected cluster.
     */
    export interface Schema$EncryptionConfig {
        /**
         * Describes the Cloud KMS encryption key that will be used to protect the destination Bigtable cluster. The requirements for this key are: 1) The Cloud Bigtable service account associated with the project that contains this cluster must be granted the `cloudkms.cryptoKeyEncrypterDecrypter` role on the CMEK key. 2) Only regional keys can be used and the region of the CMEK key must match the region of the cluster. Values are of the form `projects/{project\}/locations/{location\}/keyRings/{keyring\}/cryptoKeys/{key\}`
         */
        kmsKeyName?: string | null;
    }
    /**
     * Encryption information for a given resource. If this resource is protected with customer managed encryption, the in-use Cloud Key Management Service (Cloud KMS) key version is specified along with its status.
     */
    export interface Schema$EncryptionInfo {
        /**
         * Output only. The status of encrypt/decrypt calls on underlying data for this resource. Regardless of status, the existing data is always encrypted at rest.
         */
        encryptionStatus?: Schema$Status;
        /**
         * Output only. The type of encryption used to protect this resource.
         */
        encryptionType?: string | null;
        /**
         * Output only. The version of the Cloud KMS key specified in the parent cluster that is in use for the data underlying this table.
         */
        kmsKeyVersion?: string | null;
    }
    /**
     * Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: "Summary size limit" description: "Determines if a summary is less than 100 chars" expression: "document.summary.size() < 100" Example (Equality): title: "Requestor is owner" description: "Determines if requestor is the document owner" expression: "document.owner == request.auth.claims.email" Example (Logic): title: "Public documents" description: "Determine whether the document should be publicly visible" expression: "document.type != 'private' && document.type != 'internal'" Example (Data Manipulation): title: "Notification string" description: "Create a notification string with a timestamp." expression: "'New message received at ' + string(document.create_time)" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.
     */
    export interface Schema$Expr {
        /**
         * Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.
         */
        description?: string | null;
        /**
         * Textual representation of an expression in Common Expression Language syntax.
         */
        expression?: string | null;
        /**
         * Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.
         */
        location?: string | null;
        /**
         * Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.
         */
        title?: string | null;
    }
    /**
     * Rule for determining which cells to delete during garbage collection.
     */
    export interface Schema$GcRule {
        /**
         * Delete cells that would be deleted by every nested rule.
         */
        intersection?: Schema$Intersection;
        /**
         * Delete cells in a column older than the given age. Values must be at least one millisecond, and will be truncated to microsecond granularity.
         */
        maxAge?: string | null;
        /**
         * Delete all cells in a column except the most recent N.
         */
        maxNumVersions?: number | null;
        /**
         * Delete cells that would be deleted by any nested rule.
         */
        union?: Schema$Union;
    }
    /**
     * Request message for google.bigtable.admin.v2.BigtableTableAdmin.GenerateConsistencyToken
     */
    export interface Schema$GenerateConsistencyTokenRequest {
    }
    /**
     * Response message for google.bigtable.admin.v2.BigtableTableAdmin.GenerateConsistencyToken
     */
    export interface Schema$GenerateConsistencyTokenResponse {
        /**
         * The generated consistency token.
         */
        consistencyToken?: string | null;
    }
    /**
     * Request message for `GetIamPolicy` method.
     */
    export interface Schema$GetIamPolicyRequest {
        /**
         * OPTIONAL: A `GetPolicyOptions` object for specifying options to `GetIamPolicy`.
         */
        options?: Schema$GetPolicyOptions;
    }
    /**
     * Encapsulates settings provided to GetIamPolicy.
     */
    export interface Schema$GetPolicyOptions {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        requestedPolicyVersion?: number | null;
    }
    /**
     * A tablet is a defined by a start and end key and is explained in https://cloud.google.com/bigtable/docs/overview#architecture and https://cloud.google.com/bigtable/docs/performance#optimization. A Hot tablet is a tablet that exhibits high average cpu usage during the time interval from start time to end time.
     */
    export interface Schema$HotTablet {
        /**
         * Tablet End Key (inclusive).
         */
        endKey?: string | null;
        /**
         * Output only. The end time of the hot tablet.
         */
        endTime?: string | null;
        /**
         * The unique name of the hot tablet. Values are of the form `projects/{project\}/instances/{instance\}/clusters/{cluster\}/hotTablets/[a-zA-Z0-9_-]*`.
         */
        name?: string | null;
        /**
         * Output only. The average CPU usage spent by a node on this tablet over the start_time to end_time time range. The percentage is the amount of CPU used by the node to serve the tablet, from 0% (tablet was not interacted with) to 100% (the node spent all cycles serving the hot tablet).
         */
        nodeCpuUsagePercent?: number | null;
        /**
         * Tablet Start Key (inclusive).
         */
        startKey?: string | null;
        /**
         * Output only. The start time of the hot tablet.
         */
        startTime?: string | null;
        /**
         * Name of the table that contains the tablet. Values are of the form `projects/{project\}/instances/{instance\}/tables/_a-zA-Z0-9*`.
         */
        tableName?: string | null;
    }
    /**
     * A collection of Bigtable Tables and the resources that serve them. All tables in an instance are served from all Clusters in the instance.
     */
    export interface Schema$Instance {
        /**
         * Output only. A commit timestamp representing when this Instance was created. For instances created before this field was added (August 2021), this value is `seconds: 0, nanos: 1`.
         */
        createTime?: string | null;
        /**
         * Required. The descriptive name for this instance as it appears in UIs. Can be changed at any time, but should be kept globally unique to avoid confusion.
         */
        displayName?: string | null;
        /**
         * Labels are a flexible and lightweight mechanism for organizing cloud resources into groups that reflect a customer's organizational needs and deployment strategies. They can be used to filter resources and aggregate metrics. * Label keys must be between 1 and 63 characters long and must conform to the regular expression: `\p{Ll\}\p{Lo\}{0,62\}`. * Label values must be between 0 and 63 characters long and must conform to the regular expression: `[\p{Ll\}\p{Lo\}\p{N\}_-]{0,63\}`. * No more than 64 labels can be associated with a given resource. * Keys and values must both be under 128 bytes.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The unique name of the instance. Values are of the form `projects/{project\}/instances/a-z+[a-z0-9]`.
         */
        name?: string | null;
        /**
         * Output only. Reserved for future use.
         */
        satisfiesPzs?: boolean | null;
        /**
         * Output only. The current state of the instance.
         */
        state?: string | null;
        /**
         * The type of the instance. Defaults to `PRODUCTION`.
         */
        type?: string | null;
    }
    /**
     * A GcRule which deletes cells matching all of the given rules.
     */
    export interface Schema$Intersection {
        /**
         * Only delete cells which would be deleted by every element of `rules`.
         */
        rules?: Schema$GcRule[];
    }
    /**
     * Response message for BigtableInstanceAdmin.ListAppProfiles.
     */
    export interface Schema$ListAppProfilesResponse {
        /**
         * The list of requested app profiles.
         */
        appProfiles?: Schema$AppProfile[];
        /**
         * Locations from which AppProfile information could not be retrieved, due to an outage or some other transient condition. AppProfiles from these locations may be missing from `app_profiles`. Values are of the form `projects//locations/`
         */
        failedLocations?: string[] | null;
        /**
         * Set if not all app profiles could be returned in a single response. Pass this value to `page_token` in another request to get the next page of results.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response for ListBackups.
     */
    export interface Schema$ListBackupsResponse {
        /**
         * The list of matching backups.
         */
        backups?: Schema$Backup[];
        /**
         * `next_page_token` can be sent in a subsequent ListBackups call to fetch more of the matching backups.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message for BigtableInstanceAdmin.ListClusters.
     */
    export interface Schema$ListClustersResponse {
        /**
         * The list of requested clusters.
         */
        clusters?: Schema$Cluster[];
        /**
         * Locations from which Cluster information could not be retrieved, due to an outage or some other transient condition. Clusters from these locations may be missing from `clusters`, or may only have partial information returned. Values are of the form `projects//locations/`
         */
        failedLocations?: string[] | null;
        /**
         * DEPRECATED: This field is unused and ignored.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message for BigtableInstanceAdmin.ListHotTablets.
     */
    export interface Schema$ListHotTabletsResponse {
        /**
         * List of hot tablets in the tables of the requested cluster that fall within the requested time range. Hot tablets are ordered by node cpu usage percent. If there are multiple hot tablets that correspond to the same tablet within a 15-minute interval, only the hot tablet with the highest node cpu usage will be included in the response.
         */
        hotTablets?: Schema$HotTablet[];
        /**
         * Set if not all hot tablets could be returned in a single response. Pass this value to `page_token` in another request to get the next page of results.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message for BigtableInstanceAdmin.ListInstances.
     */
    export interface Schema$ListInstancesResponse {
        /**
         * Locations from which Instance information could not be retrieved, due to an outage or some other transient condition. Instances whose Clusters are all in one of the failed locations may be missing from `instances`, and Instances with at least one Cluster in a failed location may only have partial information returned. Values are of the form `projects//locations/`
         */
        failedLocations?: string[] | null;
        /**
         * The list of requested instances.
         */
        instances?: Schema$Instance[];
        /**
         * DEPRECATED: This field is unused and ignored.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Locations.ListLocations.
     */
    export interface Schema$ListLocationsResponse {
        /**
         * A list of locations that matches the specified filter in the request.
         */
        locations?: Schema$Location[];
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$ListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$Operation[];
    }
    /**
     * Response message for google.bigtable.admin.v2.BigtableTableAdmin.ListTables
     */
    export interface Schema$ListTablesResponse {
        /**
         * Set if not all tables could be returned in a single response. Pass this value to `page_token` in another request to get the next page of results.
         */
        nextPageToken?: string | null;
        /**
         * The tables present in the requested instance.
         */
        tables?: Schema$Table[];
    }
    /**
     * A resource that represents a Google Cloud location.
     */
    export interface Schema$Location {
        /**
         * The friendly name for this location, typically a nearby city name. For example, "Tokyo".
         */
        displayName?: string | null;
        /**
         * Cross-service attributes for the location. For example {"cloud.googleapis.com/region": "us-east1"\}
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The canonical id for this location. For example: `"us-east1"`.
         */
        locationId?: string | null;
        /**
         * Service-specific metadata. For example the available capacity at the given location.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * Resource name for the location, which may vary between implementations. For example: `"projects/example-project/locations/us-east1"`
         */
        name?: string | null;
    }
    /**
     * A create, update, or delete of a particular column family.
     */
    export interface Schema$Modification {
        /**
         * Create a new column family with the specified schema, or fail if one already exists with the given ID.
         */
        create?: Schema$ColumnFamily;
        /**
         * Drop (delete) the column family with the given ID, or fail if no such family exists.
         */
        drop?: boolean | null;
        /**
         * The ID of the column family to be modified.
         */
        id?: string | null;
        /**
         * Update an existing column family to the specified schema, or fail if no column family exists with the given ID.
         */
        update?: Schema$ColumnFamily;
    }
    /**
     * Request message for google.bigtable.admin.v2.BigtableTableAdmin.ModifyColumnFamilies
     */
    export interface Schema$ModifyColumnFamiliesRequest {
        /**
         * Required. Modifications to be atomically applied to the specified table's families. Entries are applied in order, meaning that earlier modifications can be masked by later ones (in the case of repeated updates to the same family, for example).
         */
        modifications?: Schema$Modification[];
    }
    /**
     * Read/write requests are routed to the nearest cluster in the instance, and will fail over to the nearest cluster that is available in the event of transient errors or delays. Clusters in a region are considered equidistant. Choosing this option sacrifices read-your-writes consistency to improve availability.
     */
    export interface Schema$MultiClusterRoutingUseAny {
        /**
         * The set of clusters to route to. The order is ignored; clusters will be tried in order of distance. If left empty, all clusters are eligible.
         */
        clusterIds?: string[] | null;
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$Operation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * Encapsulates progress related information for a Cloud Bigtable long running operation.
     */
    export interface Schema$OperationProgress {
        /**
         * If set, the time at which this operation failed or was completed successfully.
         */
        endTime?: string | null;
        /**
         * Percent completion of the operation. Values are between 0 and 100 inclusive.
         */
        progressPercent?: number | null;
        /**
         * Time the request was received.
         */
        startTime?: string | null;
    }
    /**
     * Metadata type for the long-running operation used to track the progress of optimizations performed on a newly restored table. This long-running operation is automatically created by the system after the successful completion of a table restore, and cannot be cancelled.
     */
    export interface Schema$OptimizeRestoredTableMetadata {
        /**
         * Name of the restored table being optimized.
         */
        name?: string | null;
        /**
         * The progress of the post-restore optimizations.
         */
        progress?: Schema$OperationProgress;
    }
    /**
     * The metadata for the Operation returned by PartialUpdateCluster.
     */
    export interface Schema$PartialUpdateClusterMetadata {
        /**
         * The time at which the operation failed or was completed successfully.
         */
        finishTime?: string | null;
        /**
         * The original request for PartialUpdateCluster.
         */
        originalRequest?: Schema$PartialUpdateClusterRequest;
        /**
         * The time at which the original request was received.
         */
        requestTime?: string | null;
    }
    /**
     * Request message for BigtableInstanceAdmin.PartialUpdateCluster.
     */
    export interface Schema$PartialUpdateClusterRequest {
        /**
         * Required. The Cluster which contains the partial updates to be applied, subject to the update_mask.
         */
        cluster?: Schema$Cluster;
        /**
         * Required. The subset of Cluster fields which should be replaced.
         */
        updateMask?: string | null;
    }
    /**
     * Request message for BigtableInstanceAdmin.PartialUpdateInstance.
     */
    export interface Schema$PartialUpdateInstanceRequest {
        /**
         * Required. The Instance which will (partially) replace the current value.
         */
        instance?: Schema$Instance;
        /**
         * Required. The subset of Instance fields which should be replaced. Must be explicitly set.
         */
        updateMask?: string | null;
    }
    /**
     * An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { "bindings": [ { "role": "roles/resourcemanager.organizationAdmin", "members": [ "user:<EMAIL>", "group:<EMAIL>", "domain:google.com", "serviceAccount:<EMAIL>" ] \}, { "role": "roles/resourcemanager.organizationViewer", "members": [ "user:<EMAIL>" ], "condition": { "title": "expirable access", "description": "Does not grant access after Sep 2020", "expression": "request.time < timestamp('2020-10-01T00:00:00.000Z')", \} \} ], "etag": "BwWWja0YfJA=", "version": 3 \} ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).
     */
    export interface Schema$Policy {
        /**
         * Specifies cloud audit logging configuration for this policy.
         */
        auditConfigs?: Schema$AuditConfig[];
        /**
         * Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.
         */
        bindings?: Schema$Binding[];
        /**
         * `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.
         */
        etag?: string | null;
        /**
         * Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        version?: number | null;
    }
    /**
     * Information about a table restore.
     */
    export interface Schema$RestoreInfo {
        /**
         * Information about the backup used to restore the table. The backup may no longer exist.
         */
        backupInfo?: Schema$BackupInfo;
        /**
         * The type of the restore source.
         */
        sourceType?: string | null;
    }
    /**
     * Metadata type for the long-running operation returned by RestoreTable.
     */
    export interface Schema$RestoreTableMetadata {
        backupInfo?: Schema$BackupInfo;
        /**
         * Name of the table being created and restored to.
         */
        name?: string | null;
        /**
         * If exists, the name of the long-running operation that will be used to track the post-restore optimization process to optimize the performance of the restored table. The metadata type of the long-running operation is OptimizeRestoreTableMetadata. The response type is Empty. This long-running operation may be automatically created by the system if applicable after the RestoreTable long-running operation completes successfully. This operation may not be created if the table is already optimized or the restore was not successful.
         */
        optimizeTableOperationName?: string | null;
        /**
         * The progress of the RestoreTable operation.
         */
        progress?: Schema$OperationProgress;
        /**
         * The type of the restore source.
         */
        sourceType?: string | null;
    }
    /**
     * The request for RestoreTable.
     */
    export interface Schema$RestoreTableRequest {
        /**
         * Name of the backup from which to restore. Values are of the form `projects//instances//clusters//backups/`.
         */
        backup?: string | null;
        /**
         * Required. The id of the table to create and restore to. This table must not already exist. The `table_id` appended to `parent` forms the full table name of the form `projects//instances//tables/`.
         */
        tableId?: string | null;
    }
    /**
     * Request message for `SetIamPolicy` method.
     */
    export interface Schema$SetIamPolicyRequest {
        /**
         * REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them.
         */
        policy?: Schema$Policy;
        /**
         * OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: "bindings, etag"`
         */
        updateMask?: string | null;
    }
    /**
     * Unconditionally routes all read/write requests to a specific cluster. This option preserves read-your-writes consistency but does not improve availability.
     */
    export interface Schema$SingleClusterRouting {
        /**
         * Whether or not `CheckAndMutateRow` and `ReadModifyWriteRow` requests are allowed by this app profile. It is unsafe to send these requests to the same table/row/column in multiple clusters.
         */
        allowTransactionalWrites?: boolean | null;
        /**
         * The cluster to which read/write requests should be routed.
         */
        clusterId?: string | null;
    }
    /**
     * An initial split point for a newly created table.
     */
    export interface Schema$Split {
        /**
         * Row key to use as an initial tablet boundary.
         */
        key?: string | null;
    }
    /**
     * Standard options for isolating this app profile's traffic from other use cases.
     */
    export interface Schema$StandardIsolation {
        /**
         * The priority of requests sent using this app profile.
         */
        priority?: string | null;
    }
    /**
     * Checks that all writes before the consistency token was generated is replicated in every cluster and readable.
     */
    export interface Schema$StandardReadRemoteWrites {
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * A collection of user data indexed by row, column, and timestamp. Each table is served using the resources of its parent cluster.
     */
    export interface Schema$Table {
        /**
         * If specified, enable the change stream on this table. Otherwise, the change stream is disabled and the change stream is not retained.
         */
        changeStreamConfig?: Schema$ChangeStreamConfig;
        /**
         * Output only. Map from cluster ID to per-cluster table state. If it could not be determined whether or not the table has data in a particular cluster (for example, if its zone is unavailable), then there will be an entry for the cluster with UNKNOWN `replication_status`. Views: `REPLICATION_VIEW`, `ENCRYPTION_VIEW`, `FULL`
         */
        clusterStates?: {
            [key: string]: Schema$ClusterState;
        } | null;
        /**
         * The column families configured for this table, mapped by column family ID. Views: `SCHEMA_VIEW`, `STATS_VIEW`, `FULL`
         */
        columnFamilies?: {
            [key: string]: Schema$ColumnFamily;
        } | null;
        /**
         * Set to true to make the table protected against data loss. i.e. deleting the following resources through Admin APIs are prohibited: * The table. * The column families in the table. * The instance containing the table. Note one can still delete the data stored in the table through Data APIs.
         */
        deletionProtection?: boolean | null;
        /**
         * Immutable. The granularity (i.e. `MILLIS`) at which timestamps are stored in this table. Timestamps not matching the granularity will be rejected. If unspecified at creation time, the value will be set to `MILLIS`. Views: `SCHEMA_VIEW`, `FULL`.
         */
        granularity?: string | null;
        /**
         * The unique name of the table. Values are of the form `projects/{project\}/instances/{instance\}/tables/_a-zA-Z0-9*`. Views: `NAME_ONLY`, `SCHEMA_VIEW`, `REPLICATION_VIEW`, `STATS_VIEW`, `FULL`
         */
        name?: string | null;
        /**
         * Output only. If this table was restored from another data source (e.g. a backup), this field will be populated with information about the restore.
         */
        restoreInfo?: Schema$RestoreInfo;
        /**
         * Output only. Only available with STATS_VIEW, this includes summary statistics about the entire table contents. For statistics about a specific column family, see ColumnFamilyStats in the mapped ColumnFamily collection above.
         */
        stats?: Schema$TableStats;
    }
    /**
     * Progress info for copying a table's data to the new cluster.
     */
    export interface Schema$TableProgress {
        /**
         * Estimate of the number of bytes copied so far for this table. This will eventually reach 'estimated_size_bytes' unless the table copy is CANCELLED.
         */
        estimatedCopiedBytes?: string | null;
        /**
         * Estimate of the size of the table to be copied.
         */
        estimatedSizeBytes?: string | null;
        state?: string | null;
    }
    /**
     * Approximate statistics related to a table. These statistics are calculated infrequently, while simultaneously, data in the table can change rapidly. Thus the values reported here (e.g. row count) are very likely out-of date, even the instant they are received in this API. Thus, only treat these values as approximate. IMPORTANT: Everything below is approximate, unless otherwise specified.
     */
    export interface Schema$TableStats {
        /**
         * How many cells are present per column (column family, column qualifier) combinations, averaged over all columns in all rows in the table. e.g. A table with 2 rows: * A row with 3 cells in "family:col" and 1 cell in "other:col" (4 cells / 2 columns) * A row with 1 cell in "family:col", 7 cells in "family:other_col", and 7 cells in "other:data" (15 cells / 3 columns) would report (4 + 15)/(2 + 3) = 3.8 in this field.
         */
        averageCellsPerColumn?: number | null;
        /**
         * How many (column family, column qualifier) combinations are present per row in the table, averaged over all rows in the table. e.g. A table with 2 rows: * A row with cells in "family:col" and "other:col" (2 distinct columns) * A row with cells in "family:col", "family:other_col", and "other:data" (3 distinct columns) would report (2 + 3)/2 = 2.5 in this field.
         */
        averageColumnsPerRow?: number | null;
        /**
         * This is roughly how many bytes would be needed to read the entire table (e.g. by streaming all contents out).
         */
        logicalDataBytes?: string | null;
        /**
         * How many rows are in the table.
         */
        rowCount?: string | null;
    }
    /**
     * Request message for `TestIamPermissions` method.
     */
    export interface Schema$TestIamPermissionsRequest {
        /**
         * The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).
         */
        permissions?: string[] | null;
    }
    /**
     * Response message for `TestIamPermissions` method.
     */
    export interface Schema$TestIamPermissionsResponse {
        /**
         * A subset of `TestPermissionsRequest.permissions` that the caller is allowed.
         */
        permissions?: string[] | null;
    }
    /**
     * Metadata type for the operation returned by google.bigtable.admin.v2.BigtableTableAdmin.UndeleteTable.
     */
    export interface Schema$UndeleteTableMetadata {
        /**
         * If set, the time at which this operation finished or was cancelled.
         */
        endTime?: string | null;
        /**
         * The name of the table being restored.
         */
        name?: string | null;
        /**
         * The time at which this operation started.
         */
        startTime?: string | null;
    }
    /**
     * Request message for google.bigtable.admin.v2.BigtableTableAdmin.UndeleteTable
     */
    export interface Schema$UndeleteTableRequest {
    }
    /**
     * A GcRule which deletes cells matching any of the given rules.
     */
    export interface Schema$Union {
        /**
         * Delete cells which would be deleted by any element of `rules`.
         */
        rules?: Schema$GcRule[];
    }
    /**
     * The metadata for the Operation returned by UpdateAppProfile.
     */
    export interface Schema$UpdateAppProfileMetadata {
    }
    /**
     * The metadata for the Operation returned by UpdateCluster.
     */
    export interface Schema$UpdateClusterMetadata {
        /**
         * The time at which the operation failed or was completed successfully.
         */
        finishTime?: string | null;
        /**
         * The request that prompted the initiation of this UpdateCluster operation.
         */
        originalRequest?: Schema$Cluster;
        /**
         * The time at which the original request was received.
         */
        requestTime?: string | null;
    }
    /**
     * The metadata for the Operation returned by UpdateInstance.
     */
    export interface Schema$UpdateInstanceMetadata {
        /**
         * The time at which the operation failed or was completed successfully.
         */
        finishTime?: string | null;
        /**
         * The request that prompted the initiation of this UpdateInstance operation.
         */
        originalRequest?: Schema$PartialUpdateInstanceRequest;
        /**
         * The time at which the original request was received.
         */
        requestTime?: string | null;
    }
    /**
     * Metadata type for the operation returned by UpdateTable.
     */
    export interface Schema$UpdateTableMetadata {
        /**
         * If set, the time at which this operation finished or was canceled.
         */
        endTime?: string | null;
        /**
         * The name of the table being updated.
         */
        name?: string | null;
        /**
         * The time at which this operation started.
         */
        startTime?: string | null;
    }
    export class Resource$Operations {
        context: APIRequestContext;
        projects: Resource$Operations$Projects;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Operations$Cancel, options: StreamMethodOptions): GaxiosPromise<Readable>;
        cancel(params?: Params$Resource$Operations$Cancel, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        cancel(params: Params$Resource$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(params: Params$Resource$Operations$Cancel, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Operations$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Operations$Delete, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        delete(params: Params$Resource$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Operations$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        get(params: Params$Resource$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
    }
    export interface Params$Resource$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export class Resource$Operations$Projects {
        context: APIRequestContext;
        operations: Resource$Operations$Projects$Operations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Operations$Projects$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Operations$Projects$Operations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Operations$Projects$Operations$List, options?: MethodOptions): GaxiosPromise<Schema$ListOperationsResponse>;
        list(params: Params$Resource$Operations$Projects$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Operations$Projects$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Operations$Projects$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
    }
    export interface Params$Resource$Operations$Projects$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        instances: Resource$Projects$Instances;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Instances {
        context: APIRequestContext;
        appProfiles: Resource$Projects$Instances$Appprofiles;
        clusters: Resource$Projects$Instances$Clusters;
        tables: Resource$Projects$Instances$Tables;
        constructor(context: APIRequestContext);
        /**
         * Create an instance within a project. Note that exactly one of Cluster.serve_nodes and Cluster.cluster_config.cluster_autoscaling_config can be set. If serve_nodes is set to non-zero, then the cluster is manually scaled. If cluster_config.cluster_autoscaling_config is non-empty, then autoscaling is enabled.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Instances$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Instances$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Instances$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Instances$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Instances$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Delete an instance from a project.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Instances$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Instances$Delete, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        delete(params: Params$Resource$Projects$Instances$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Instances$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Instances$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets information about an instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Instances$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Instances$Get, options?: MethodOptions): GaxiosPromise<Schema$Instance>;
        get(params: Params$Resource$Projects$Instances$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Instances$Get, options: MethodOptions | BodyResponseCallback<Schema$Instance>, callback: BodyResponseCallback<Schema$Instance>): void;
        get(params: Params$Resource$Projects$Instances$Get, callback: BodyResponseCallback<Schema$Instance>): void;
        get(callback: BodyResponseCallback<Schema$Instance>): void;
        /**
         * Gets the access control policy for an instance resource. Returns an empty policy if an instance exists but does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Instances$Getiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getIamPolicy(params?: Params$Resource$Projects$Instances$Getiampolicy, options?: MethodOptions): GaxiosPromise<Schema$Policy>;
        getIamPolicy(params: Params$Resource$Projects$Instances$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Instances$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Instances$Getiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Lists information about instances in a project.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Instances$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Instances$List, options?: MethodOptions): GaxiosPromise<Schema$ListInstancesResponse>;
        list(params: Params$Resource$Projects$Instances$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Instances$List, options: MethodOptions | BodyResponseCallback<Schema$ListInstancesResponse>, callback: BodyResponseCallback<Schema$ListInstancesResponse>): void;
        list(params: Params$Resource$Projects$Instances$List, callback: BodyResponseCallback<Schema$ListInstancesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListInstancesResponse>): void;
        /**
         * Partially updates an instance within a project. This method can modify all fields of an Instance and is the preferred way to update an Instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        partialUpdateInstance(params: Params$Resource$Projects$Instances$Partialupdateinstance, options: StreamMethodOptions): GaxiosPromise<Readable>;
        partialUpdateInstance(params?: Params$Resource$Projects$Instances$Partialupdateinstance, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        partialUpdateInstance(params: Params$Resource$Projects$Instances$Partialupdateinstance, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        partialUpdateInstance(params: Params$Resource$Projects$Instances$Partialupdateinstance, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        partialUpdateInstance(params: Params$Resource$Projects$Instances$Partialupdateinstance, callback: BodyResponseCallback<Schema$Operation>): void;
        partialUpdateInstance(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Sets the access control policy on an instance resource. Replaces any existing policy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Instances$Setiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setIamPolicy(params?: Params$Resource$Projects$Instances$Setiampolicy, options?: MethodOptions): GaxiosPromise<Schema$Policy>;
        setIamPolicy(params: Params$Resource$Projects$Instances$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Instances$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Instances$Setiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Returns permissions that the caller has on the specified instance resource.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Instances$Testiampermissions, options: StreamMethodOptions): GaxiosPromise<Readable>;
        testIamPermissions(params?: Params$Resource$Projects$Instances$Testiampermissions, options?: MethodOptions): GaxiosPromise<Schema$TestIamPermissionsResponse>;
        testIamPermissions(params: Params$Resource$Projects$Instances$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Instances$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Instances$Testiampermissions, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        /**
         * Updates an instance within a project. This method updates only the display name and type for an Instance. To update other Instance properties, such as labels, use PartialUpdateInstance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        update(params: Params$Resource$Projects$Instances$Update, options: StreamMethodOptions): GaxiosPromise<Readable>;
        update(params?: Params$Resource$Projects$Instances$Update, options?: MethodOptions): GaxiosPromise<Schema$Instance>;
        update(params: Params$Resource$Projects$Instances$Update, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        update(params: Params$Resource$Projects$Instances$Update, options: MethodOptions | BodyResponseCallback<Schema$Instance>, callback: BodyResponseCallback<Schema$Instance>): void;
        update(params: Params$Resource$Projects$Instances$Update, callback: BodyResponseCallback<Schema$Instance>): void;
        update(callback: BodyResponseCallback<Schema$Instance>): void;
    }
    export interface Params$Resource$Projects$Instances$Create extends StandardParameters {
        /**
         * Required. The unique name of the project in which to create the new instance. Values are of the form `projects/{project\}`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CreateInstanceRequest;
    }
    export interface Params$Resource$Projects$Instances$Delete extends StandardParameters {
        /**
         * Required. The unique name of the instance to be deleted. Values are of the form `projects/{project\}/instances/{instance\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Instances$Get extends StandardParameters {
        /**
         * Required. The unique name of the requested instance. Values are of the form `projects/{project\}/instances/{instance\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Instances$Getiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Instances$List extends StandardParameters {
        /**
         * DEPRECATED: This field is unused and ignored.
         */
        pageToken?: string;
        /**
         * Required. The unique name of the project for which a list of instances is requested. Values are of the form `projects/{project\}`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Instances$Partialupdateinstance extends StandardParameters {
        /**
         * The unique name of the instance. Values are of the form `projects/{project\}/instances/a-z+[a-z0-9]`.
         */
        name?: string;
        /**
         * Required. The subset of Instance fields which should be replaced. Must be explicitly set.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Instance;
    }
    export interface Params$Resource$Projects$Instances$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Instances$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TestIamPermissionsRequest;
    }
    export interface Params$Resource$Projects$Instances$Update extends StandardParameters {
        /**
         * The unique name of the instance. Values are of the form `projects/{project\}/instances/a-z+[a-z0-9]`.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Instance;
    }
    export class Resource$Projects$Instances$Appprofiles {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates an app profile within an instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Instances$Appprofiles$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Instances$Appprofiles$Create, options?: MethodOptions): GaxiosPromise<Schema$AppProfile>;
        create(params: Params$Resource$Projects$Instances$Appprofiles$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Instances$Appprofiles$Create, options: MethodOptions | BodyResponseCallback<Schema$AppProfile>, callback: BodyResponseCallback<Schema$AppProfile>): void;
        create(params: Params$Resource$Projects$Instances$Appprofiles$Create, callback: BodyResponseCallback<Schema$AppProfile>): void;
        create(callback: BodyResponseCallback<Schema$AppProfile>): void;
        /**
         * Deletes an app profile from an instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Instances$Appprofiles$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Instances$Appprofiles$Delete, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        delete(params: Params$Resource$Projects$Instances$Appprofiles$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Instances$Appprofiles$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Instances$Appprofiles$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets information about an app profile.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Instances$Appprofiles$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Instances$Appprofiles$Get, options?: MethodOptions): GaxiosPromise<Schema$AppProfile>;
        get(params: Params$Resource$Projects$Instances$Appprofiles$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Instances$Appprofiles$Get, options: MethodOptions | BodyResponseCallback<Schema$AppProfile>, callback: BodyResponseCallback<Schema$AppProfile>): void;
        get(params: Params$Resource$Projects$Instances$Appprofiles$Get, callback: BodyResponseCallback<Schema$AppProfile>): void;
        get(callback: BodyResponseCallback<Schema$AppProfile>): void;
        /**
         * Lists information about app profiles in an instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Instances$Appprofiles$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Instances$Appprofiles$List, options?: MethodOptions): GaxiosPromise<Schema$ListAppProfilesResponse>;
        list(params: Params$Resource$Projects$Instances$Appprofiles$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Instances$Appprofiles$List, options: MethodOptions | BodyResponseCallback<Schema$ListAppProfilesResponse>, callback: BodyResponseCallback<Schema$ListAppProfilesResponse>): void;
        list(params: Params$Resource$Projects$Instances$Appprofiles$List, callback: BodyResponseCallback<Schema$ListAppProfilesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListAppProfilesResponse>): void;
        /**
         * Updates an app profile within an instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Instances$Appprofiles$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Instances$Appprofiles$Patch, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        patch(params: Params$Resource$Projects$Instances$Appprofiles$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Instances$Appprofiles$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Instances$Appprofiles$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Instances$Appprofiles$Create extends StandardParameters {
        /**
         * Required. The ID to be used when referring to the new app profile within its instance, e.g., just `myprofile` rather than `projects/myproject/instances/myinstance/appProfiles/myprofile`.
         */
        appProfileId?: string;
        /**
         * If true, ignore safety checks when creating the app profile.
         */
        ignoreWarnings?: boolean;
        /**
         * Required. The unique name of the instance in which to create the new app profile. Values are of the form `projects/{project\}/instances/{instance\}`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AppProfile;
    }
    export interface Params$Resource$Projects$Instances$Appprofiles$Delete extends StandardParameters {
        /**
         * Required. If true, ignore safety checks when deleting the app profile.
         */
        ignoreWarnings?: boolean;
        /**
         * Required. The unique name of the app profile to be deleted. Values are of the form `projects/{project\}/instances/{instance\}/appProfiles/{app_profile\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Instances$Appprofiles$Get extends StandardParameters {
        /**
         * Required. The unique name of the requested app profile. Values are of the form `projects/{project\}/instances/{instance\}/appProfiles/{app_profile\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Instances$Appprofiles$List extends StandardParameters {
        /**
         * Maximum number of results per page. A page_size of zero lets the server choose the number of items to return. A page_size which is strictly positive will return at most that many items. A negative page_size will cause an error. Following the first request, subsequent paginated calls are not required to pass a page_size. If a page_size is set in subsequent calls, it must match the page_size given in the first request.
         */
        pageSize?: number;
        /**
         * The value of `next_page_token` returned by a previous call.
         */
        pageToken?: string;
        /**
         * Required. The unique name of the instance for which a list of app profiles is requested. Values are of the form `projects/{project\}/instances/{instance\}`. Use `{instance\} = '-'` to list AppProfiles for all Instances in a project, e.g., `projects/myproject/instances/-`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Instances$Appprofiles$Patch extends StandardParameters {
        /**
         * If true, ignore safety checks when updating the app profile.
         */
        ignoreWarnings?: boolean;
        /**
         * The unique name of the app profile. Values are of the form `projects/{project\}/instances/{instance\}/appProfiles/_a-zA-Z0-9*`.
         */
        name?: string;
        /**
         * Required. The subset of app profile fields which should be replaced. If unset, all fields will be replaced.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AppProfile;
    }
    export class Resource$Projects$Instances$Clusters {
        context: APIRequestContext;
        backups: Resource$Projects$Instances$Clusters$Backups;
        hotTablets: Resource$Projects$Instances$Clusters$Hottablets;
        constructor(context: APIRequestContext);
        /**
         * Creates a cluster within an instance. Note that exactly one of Cluster.serve_nodes and Cluster.cluster_config.cluster_autoscaling_config can be set. If serve_nodes is set to non-zero, then the cluster is manually scaled. If cluster_config.cluster_autoscaling_config is non-empty, then autoscaling is enabled.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Instances$Clusters$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Instances$Clusters$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Instances$Clusters$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Instances$Clusters$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Instances$Clusters$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a cluster from an instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Instances$Clusters$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Instances$Clusters$Delete, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        delete(params: Params$Resource$Projects$Instances$Clusters$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Instances$Clusters$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Instances$Clusters$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets information about a cluster.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Instances$Clusters$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Instances$Clusters$Get, options?: MethodOptions): GaxiosPromise<Schema$Cluster>;
        get(params: Params$Resource$Projects$Instances$Clusters$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Instances$Clusters$Get, options: MethodOptions | BodyResponseCallback<Schema$Cluster>, callback: BodyResponseCallback<Schema$Cluster>): void;
        get(params: Params$Resource$Projects$Instances$Clusters$Get, callback: BodyResponseCallback<Schema$Cluster>): void;
        get(callback: BodyResponseCallback<Schema$Cluster>): void;
        /**
         * Lists information about clusters in an instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Instances$Clusters$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Instances$Clusters$List, options?: MethodOptions): GaxiosPromise<Schema$ListClustersResponse>;
        list(params: Params$Resource$Projects$Instances$Clusters$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Instances$Clusters$List, options: MethodOptions | BodyResponseCallback<Schema$ListClustersResponse>, callback: BodyResponseCallback<Schema$ListClustersResponse>): void;
        list(params: Params$Resource$Projects$Instances$Clusters$List, callback: BodyResponseCallback<Schema$ListClustersResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListClustersResponse>): void;
        /**
         * Partially updates a cluster within a project. This method is the preferred way to update a Cluster. To enable and update autoscaling, set cluster_config.cluster_autoscaling_config. When autoscaling is enabled, serve_nodes is treated as an OUTPUT_ONLY field, meaning that updates to it are ignored. Note that an update cannot simultaneously set serve_nodes to non-zero and cluster_config.cluster_autoscaling_config to non-empty, and also specify both in the update_mask. To disable autoscaling, clear cluster_config.cluster_autoscaling_config, and explicitly set a serve_node count via the update_mask.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        partialUpdateCluster(params: Params$Resource$Projects$Instances$Clusters$Partialupdatecluster, options: StreamMethodOptions): GaxiosPromise<Readable>;
        partialUpdateCluster(params?: Params$Resource$Projects$Instances$Clusters$Partialupdatecluster, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        partialUpdateCluster(params: Params$Resource$Projects$Instances$Clusters$Partialupdatecluster, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        partialUpdateCluster(params: Params$Resource$Projects$Instances$Clusters$Partialupdatecluster, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        partialUpdateCluster(params: Params$Resource$Projects$Instances$Clusters$Partialupdatecluster, callback: BodyResponseCallback<Schema$Operation>): void;
        partialUpdateCluster(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Updates a cluster within an instance. Note that UpdateCluster does not support updating cluster_config.cluster_autoscaling_config. In order to update it, you must use PartialUpdateCluster.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        update(params: Params$Resource$Projects$Instances$Clusters$Update, options: StreamMethodOptions): GaxiosPromise<Readable>;
        update(params?: Params$Resource$Projects$Instances$Clusters$Update, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        update(params: Params$Resource$Projects$Instances$Clusters$Update, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        update(params: Params$Resource$Projects$Instances$Clusters$Update, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        update(params: Params$Resource$Projects$Instances$Clusters$Update, callback: BodyResponseCallback<Schema$Operation>): void;
        update(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Instances$Clusters$Create extends StandardParameters {
        /**
         * Required. The ID to be used when referring to the new cluster within its instance, e.g., just `mycluster` rather than `projects/myproject/instances/myinstance/clusters/mycluster`.
         */
        clusterId?: string;
        /**
         * Required. The unique name of the instance in which to create the new cluster. Values are of the form `projects/{project\}/instances/{instance\}`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Cluster;
    }
    export interface Params$Resource$Projects$Instances$Clusters$Delete extends StandardParameters {
        /**
         * Required. The unique name of the cluster to be deleted. Values are of the form `projects/{project\}/instances/{instance\}/clusters/{cluster\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Instances$Clusters$Get extends StandardParameters {
        /**
         * Required. The unique name of the requested cluster. Values are of the form `projects/{project\}/instances/{instance\}/clusters/{cluster\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Instances$Clusters$List extends StandardParameters {
        /**
         * DEPRECATED: This field is unused and ignored.
         */
        pageToken?: string;
        /**
         * Required. The unique name of the instance for which a list of clusters is requested. Values are of the form `projects/{project\}/instances/{instance\}`. Use `{instance\} = '-'` to list Clusters for all Instances in a project, e.g., `projects/myproject/instances/-`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Instances$Clusters$Partialupdatecluster extends StandardParameters {
        /**
         * The unique name of the cluster. Values are of the form `projects/{project\}/instances/{instance\}/clusters/a-z*`.
         */
        name?: string;
        /**
         * Required. The subset of Cluster fields which should be replaced.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Cluster;
    }
    export interface Params$Resource$Projects$Instances$Clusters$Update extends StandardParameters {
        /**
         * The unique name of the cluster. Values are of the form `projects/{project\}/instances/{instance\}/clusters/a-z*`.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Cluster;
    }
    export class Resource$Projects$Instances$Clusters$Backups {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Copy a Cloud Bigtable backup to a new backup in the destination cluster located in the destination instance and project.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        copy(params: Params$Resource$Projects$Instances$Clusters$Backups$Copy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        copy(params?: Params$Resource$Projects$Instances$Clusters$Backups$Copy, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        copy(params: Params$Resource$Projects$Instances$Clusters$Backups$Copy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        copy(params: Params$Resource$Projects$Instances$Clusters$Backups$Copy, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        copy(params: Params$Resource$Projects$Instances$Clusters$Backups$Copy, callback: BodyResponseCallback<Schema$Operation>): void;
        copy(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Starts creating a new Cloud Bigtable Backup. The returned backup long-running operation can be used to track creation of the backup. The metadata field type is CreateBackupMetadata. The response field type is Backup, if successful. Cancelling the returned operation will stop the creation and delete the backup.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Instances$Clusters$Backups$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Instances$Clusters$Backups$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Instances$Clusters$Backups$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Instances$Clusters$Backups$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Instances$Clusters$Backups$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a pending or completed Cloud Bigtable backup.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Instances$Clusters$Backups$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Instances$Clusters$Backups$Delete, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        delete(params: Params$Resource$Projects$Instances$Clusters$Backups$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Instances$Clusters$Backups$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Instances$Clusters$Backups$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets metadata on a pending or completed Cloud Bigtable Backup.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Instances$Clusters$Backups$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Instances$Clusters$Backups$Get, options?: MethodOptions): GaxiosPromise<Schema$Backup>;
        get(params: Params$Resource$Projects$Instances$Clusters$Backups$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Instances$Clusters$Backups$Get, options: MethodOptions | BodyResponseCallback<Schema$Backup>, callback: BodyResponseCallback<Schema$Backup>): void;
        get(params: Params$Resource$Projects$Instances$Clusters$Backups$Get, callback: BodyResponseCallback<Schema$Backup>): void;
        get(callback: BodyResponseCallback<Schema$Backup>): void;
        /**
         * Gets the access control policy for a Table or Backup resource. Returns an empty policy if the resource exists but does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Instances$Clusters$Backups$Getiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getIamPolicy(params?: Params$Resource$Projects$Instances$Clusters$Backups$Getiampolicy, options?: MethodOptions): GaxiosPromise<Schema$Policy>;
        getIamPolicy(params: Params$Resource$Projects$Instances$Clusters$Backups$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Instances$Clusters$Backups$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Instances$Clusters$Backups$Getiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Lists Cloud Bigtable backups. Returns both completed and pending backups.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Instances$Clusters$Backups$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Instances$Clusters$Backups$List, options?: MethodOptions): GaxiosPromise<Schema$ListBackupsResponse>;
        list(params: Params$Resource$Projects$Instances$Clusters$Backups$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Instances$Clusters$Backups$List, options: MethodOptions | BodyResponseCallback<Schema$ListBackupsResponse>, callback: BodyResponseCallback<Schema$ListBackupsResponse>): void;
        list(params: Params$Resource$Projects$Instances$Clusters$Backups$List, callback: BodyResponseCallback<Schema$ListBackupsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListBackupsResponse>): void;
        /**
         * Updates a pending or completed Cloud Bigtable Backup.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Instances$Clusters$Backups$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Instances$Clusters$Backups$Patch, options?: MethodOptions): GaxiosPromise<Schema$Backup>;
        patch(params: Params$Resource$Projects$Instances$Clusters$Backups$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Instances$Clusters$Backups$Patch, options: MethodOptions | BodyResponseCallback<Schema$Backup>, callback: BodyResponseCallback<Schema$Backup>): void;
        patch(params: Params$Resource$Projects$Instances$Clusters$Backups$Patch, callback: BodyResponseCallback<Schema$Backup>): void;
        patch(callback: BodyResponseCallback<Schema$Backup>): void;
        /**
         * Sets the access control policy on a Table or Backup resource. Replaces any existing policy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Instances$Clusters$Backups$Setiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setIamPolicy(params?: Params$Resource$Projects$Instances$Clusters$Backups$Setiampolicy, options?: MethodOptions): GaxiosPromise<Schema$Policy>;
        setIamPolicy(params: Params$Resource$Projects$Instances$Clusters$Backups$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Instances$Clusters$Backups$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Instances$Clusters$Backups$Setiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Returns permissions that the caller has on the specified Table or Backup resource.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Instances$Clusters$Backups$Testiampermissions, options: StreamMethodOptions): GaxiosPromise<Readable>;
        testIamPermissions(params?: Params$Resource$Projects$Instances$Clusters$Backups$Testiampermissions, options?: MethodOptions): GaxiosPromise<Schema$TestIamPermissionsResponse>;
        testIamPermissions(params: Params$Resource$Projects$Instances$Clusters$Backups$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Instances$Clusters$Backups$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Instances$Clusters$Backups$Testiampermissions, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Instances$Clusters$Backups$Copy extends StandardParameters {
        /**
         * Required. The name of the destination cluster that will contain the backup copy. The cluster must already exists. Values are of the form: `projects/{project\}/instances/{instance\}/clusters/{cluster\}`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CopyBackupRequest;
    }
    export interface Params$Resource$Projects$Instances$Clusters$Backups$Create extends StandardParameters {
        /**
         * Required. The id of the backup to be created. The `backup_id` along with the parent `parent` are combined as {parent\}/backups/{backup_id\} to create the full backup name, of the form: `projects/{project\}/instances/{instance\}/clusters/{cluster\}/backups/{backup_id\}`. This string must be between 1 and 50 characters in length and match the regex _a-zA-Z0-9*.
         */
        backupId?: string;
        /**
         * Required. This must be one of the clusters in the instance in which this table is located. The backup will be stored in this cluster. Values are of the form `projects/{project\}/instances/{instance\}/clusters/{cluster\}`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Backup;
    }
    export interface Params$Resource$Projects$Instances$Clusters$Backups$Delete extends StandardParameters {
        /**
         * Required. Name of the backup to delete. Values are of the form `projects/{project\}/instances/{instance\}/clusters/{cluster\}/backups/{backup\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Instances$Clusters$Backups$Get extends StandardParameters {
        /**
         * Required. Name of the backup. Values are of the form `projects/{project\}/instances/{instance\}/clusters/{cluster\}/backups/{backup\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Instances$Clusters$Backups$Getiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Instances$Clusters$Backups$List extends StandardParameters {
        /**
         * A filter expression that filters backups listed in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be <, \>, <=, \>=, !=, =, or :. Colon ':' represents a HAS operator which is roughly synonymous with equality. Filter rules are case insensitive. The fields eligible for filtering are: * `name` * `source_table` * `state` * `start_time` (and values are of the format YYYY-MM-DDTHH:MM:SSZ) * `end_time` (and values are of the format YYYY-MM-DDTHH:MM:SSZ) * `expire_time` (and values are of the format YYYY-MM-DDTHH:MM:SSZ) * `size_bytes` To filter on multiple expressions, provide each separate expression within parentheses. By default, each expression is an AND expression. However, you can include AND, OR, and NOT expressions explicitly. Some examples of using filters are: * `name:"exact"` --\> The backup's name is the string "exact". * `name:howl` --\> The backup's name contains the string "howl". * `source_table:prod` --\> The source_table's name contains the string "prod". * `state:CREATING` --\> The backup is pending creation. * `state:READY` --\> The backup is fully created and ready for use. * `(name:howl) AND (start_time < \"2018-03-28T14:50:00Z\")` --\> The backup name contains the string "howl" and start_time of the backup is before 2018-03-28T14:50:00Z. * `size_bytes \> 10000000000` --\> The backup's size is greater than 10GB
         */
        filter?: string;
        /**
         * An expression for specifying the sort order of the results of the request. The string value should specify one or more fields in Backup. The full syntax is described at https://aip.dev/132#ordering. Fields supported are: * name * source_table * expire_time * start_time * end_time * size_bytes * state For example, "start_time". The default sorting order is ascending. To specify descending order for the field, a suffix " desc" should be appended to the field name. For example, "start_time desc". Redundant space characters in the syntax are insigificant. If order_by is empty, results will be sorted by `start_time` in descending order starting from the most recently created backup.
         */
        orderBy?: string;
        /**
         * Number of backups to be returned in the response. If 0 or less, defaults to the server's maximum allowed page size.
         */
        pageSize?: number;
        /**
         * If non-empty, `page_token` should contain a next_page_token from a previous ListBackupsResponse to the same `parent` and with the same `filter`.
         */
        pageToken?: string;
        /**
         * Required. The cluster to list backups from. Values are of the form `projects/{project\}/instances/{instance\}/clusters/{cluster\}`. Use `{cluster\} = '-'` to list backups for all clusters in an instance, e.g., `projects/{project\}/instances/{instance\}/clusters/-`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Instances$Clusters$Backups$Patch extends StandardParameters {
        /**
         * A globally unique identifier for the backup which cannot be changed. Values are of the form `projects/{project\}/instances/{instance\}/clusters/{cluster\}/ backups/_a-zA-Z0-9*` The final segment of the name must be between 1 and 50 characters in length. The backup is stored in the cluster identified by the prefix of the backup name of the form `projects/{project\}/instances/{instance\}/clusters/{cluster\}`.
         */
        name?: string;
        /**
         * Required. A mask specifying which fields (e.g. `expire_time`) in the Backup resource should be updated. This mask is relative to the Backup resource, not to the request message. The field mask must always be specified; this prevents any future fields from being erased accidentally by clients that do not know about them.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Backup;
    }
    export interface Params$Resource$Projects$Instances$Clusters$Backups$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Instances$Clusters$Backups$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TestIamPermissionsRequest;
    }
    export class Resource$Projects$Instances$Clusters$Hottablets {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Lists hot tablets in a cluster, within the time range provided. Hot tablets are ordered based on CPU usage.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Instances$Clusters$Hottablets$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Instances$Clusters$Hottablets$List, options?: MethodOptions): GaxiosPromise<Schema$ListHotTabletsResponse>;
        list(params: Params$Resource$Projects$Instances$Clusters$Hottablets$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Instances$Clusters$Hottablets$List, options: MethodOptions | BodyResponseCallback<Schema$ListHotTabletsResponse>, callback: BodyResponseCallback<Schema$ListHotTabletsResponse>): void;
        list(params: Params$Resource$Projects$Instances$Clusters$Hottablets$List, callback: BodyResponseCallback<Schema$ListHotTabletsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListHotTabletsResponse>): void;
    }
    export interface Params$Resource$Projects$Instances$Clusters$Hottablets$List extends StandardParameters {
        /**
         * The end time to list hot tablets.
         */
        endTime?: string;
        /**
         * Maximum number of results per page. A page_size that is empty or zero lets the server choose the number of items to return. A page_size which is strictly positive will return at most that many items. A negative page_size will cause an error. Following the first request, subsequent paginated calls do not need a page_size field. If a page_size is set in subsequent calls, it must match the page_size given in the first request.
         */
        pageSize?: number;
        /**
         * The value of `next_page_token` returned by a previous call.
         */
        pageToken?: string;
        /**
         * Required. The cluster name to list hot tablets. Value is in the following form: `projects/{project\}/instances/{instance\}/clusters/{cluster\}`.
         */
        parent?: string;
        /**
         * The start time to list hot tablets. The hot tablets in the response will have start times between the requested start time and end time. Start time defaults to Now if it is unset, and end time defaults to Now - 24 hours if it is unset. The start time should be less than the end time, and the maximum allowed time range between start time and end time is 48 hours. Start time and end time should have values between Now and Now - 14 days.
         */
        startTime?: string;
    }
    export class Resource$Projects$Instances$Tables {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Checks replication consistency based on a consistency token, that is, if replication has caught up based on the conditions specified in the token and the check request.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        checkConsistency(params: Params$Resource$Projects$Instances$Tables$Checkconsistency, options: StreamMethodOptions): GaxiosPromise<Readable>;
        checkConsistency(params?: Params$Resource$Projects$Instances$Tables$Checkconsistency, options?: MethodOptions): GaxiosPromise<Schema$CheckConsistencyResponse>;
        checkConsistency(params: Params$Resource$Projects$Instances$Tables$Checkconsistency, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        checkConsistency(params: Params$Resource$Projects$Instances$Tables$Checkconsistency, options: MethodOptions | BodyResponseCallback<Schema$CheckConsistencyResponse>, callback: BodyResponseCallback<Schema$CheckConsistencyResponse>): void;
        checkConsistency(params: Params$Resource$Projects$Instances$Tables$Checkconsistency, callback: BodyResponseCallback<Schema$CheckConsistencyResponse>): void;
        checkConsistency(callback: BodyResponseCallback<Schema$CheckConsistencyResponse>): void;
        /**
         * Creates a new table in the specified instance. The table can be created with a full set of initial column families, specified in the request.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Instances$Tables$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Instances$Tables$Create, options?: MethodOptions): GaxiosPromise<Schema$Table>;
        create(params: Params$Resource$Projects$Instances$Tables$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Instances$Tables$Create, options: MethodOptions | BodyResponseCallback<Schema$Table>, callback: BodyResponseCallback<Schema$Table>): void;
        create(params: Params$Resource$Projects$Instances$Tables$Create, callback: BodyResponseCallback<Schema$Table>): void;
        create(callback: BodyResponseCallback<Schema$Table>): void;
        /**
         * Permanently deletes a specified table and all of its data.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Instances$Tables$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Instances$Tables$Delete, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        delete(params: Params$Resource$Projects$Instances$Tables$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Instances$Tables$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Instances$Tables$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Permanently drop/delete a row range from a specified table. The request can specify whether to delete all rows in a table, or only those that match a particular prefix.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        dropRowRange(params: Params$Resource$Projects$Instances$Tables$Droprowrange, options: StreamMethodOptions): GaxiosPromise<Readable>;
        dropRowRange(params?: Params$Resource$Projects$Instances$Tables$Droprowrange, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        dropRowRange(params: Params$Resource$Projects$Instances$Tables$Droprowrange, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        dropRowRange(params: Params$Resource$Projects$Instances$Tables$Droprowrange, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        dropRowRange(params: Params$Resource$Projects$Instances$Tables$Droprowrange, callback: BodyResponseCallback<Schema$Empty>): void;
        dropRowRange(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Generates a consistency token for a Table, which can be used in CheckConsistency to check whether mutations to the table that finished before this call started have been replicated. The tokens will be available for 90 days.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        generateConsistencyToken(params: Params$Resource$Projects$Instances$Tables$Generateconsistencytoken, options: StreamMethodOptions): GaxiosPromise<Readable>;
        generateConsistencyToken(params?: Params$Resource$Projects$Instances$Tables$Generateconsistencytoken, options?: MethodOptions): GaxiosPromise<Schema$GenerateConsistencyTokenResponse>;
        generateConsistencyToken(params: Params$Resource$Projects$Instances$Tables$Generateconsistencytoken, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        generateConsistencyToken(params: Params$Resource$Projects$Instances$Tables$Generateconsistencytoken, options: MethodOptions | BodyResponseCallback<Schema$GenerateConsistencyTokenResponse>, callback: BodyResponseCallback<Schema$GenerateConsistencyTokenResponse>): void;
        generateConsistencyToken(params: Params$Resource$Projects$Instances$Tables$Generateconsistencytoken, callback: BodyResponseCallback<Schema$GenerateConsistencyTokenResponse>): void;
        generateConsistencyToken(callback: BodyResponseCallback<Schema$GenerateConsistencyTokenResponse>): void;
        /**
         * Gets metadata information about the specified table.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Instances$Tables$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Instances$Tables$Get, options?: MethodOptions): GaxiosPromise<Schema$Table>;
        get(params: Params$Resource$Projects$Instances$Tables$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Instances$Tables$Get, options: MethodOptions | BodyResponseCallback<Schema$Table>, callback: BodyResponseCallback<Schema$Table>): void;
        get(params: Params$Resource$Projects$Instances$Tables$Get, callback: BodyResponseCallback<Schema$Table>): void;
        get(callback: BodyResponseCallback<Schema$Table>): void;
        /**
         * Gets the access control policy for a Table or Backup resource. Returns an empty policy if the resource exists but does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Instances$Tables$Getiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getIamPolicy(params?: Params$Resource$Projects$Instances$Tables$Getiampolicy, options?: MethodOptions): GaxiosPromise<Schema$Policy>;
        getIamPolicy(params: Params$Resource$Projects$Instances$Tables$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Instances$Tables$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Instances$Tables$Getiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Lists all tables served from a specified instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Instances$Tables$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Instances$Tables$List, options?: MethodOptions): GaxiosPromise<Schema$ListTablesResponse>;
        list(params: Params$Resource$Projects$Instances$Tables$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Instances$Tables$List, options: MethodOptions | BodyResponseCallback<Schema$ListTablesResponse>, callback: BodyResponseCallback<Schema$ListTablesResponse>): void;
        list(params: Params$Resource$Projects$Instances$Tables$List, callback: BodyResponseCallback<Schema$ListTablesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListTablesResponse>): void;
        /**
         * Performs a series of column family modifications on the specified table. Either all or none of the modifications will occur before this method returns, but data requests received prior to that point may see a table where only some modifications have taken effect.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        modifyColumnFamilies(params: Params$Resource$Projects$Instances$Tables$Modifycolumnfamilies, options: StreamMethodOptions): GaxiosPromise<Readable>;
        modifyColumnFamilies(params?: Params$Resource$Projects$Instances$Tables$Modifycolumnfamilies, options?: MethodOptions): GaxiosPromise<Schema$Table>;
        modifyColumnFamilies(params: Params$Resource$Projects$Instances$Tables$Modifycolumnfamilies, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        modifyColumnFamilies(params: Params$Resource$Projects$Instances$Tables$Modifycolumnfamilies, options: MethodOptions | BodyResponseCallback<Schema$Table>, callback: BodyResponseCallback<Schema$Table>): void;
        modifyColumnFamilies(params: Params$Resource$Projects$Instances$Tables$Modifycolumnfamilies, callback: BodyResponseCallback<Schema$Table>): void;
        modifyColumnFamilies(callback: BodyResponseCallback<Schema$Table>): void;
        /**
         * Updates a specified table.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Instances$Tables$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Instances$Tables$Patch, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        patch(params: Params$Resource$Projects$Instances$Tables$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Instances$Tables$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Instances$Tables$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Create a new table by restoring from a completed backup. The returned table long-running operation can be used to track the progress of the operation, and to cancel it. The metadata field type is RestoreTableMetadata. The response type is Table, if successful.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        restore(params: Params$Resource$Projects$Instances$Tables$Restore, options: StreamMethodOptions): GaxiosPromise<Readable>;
        restore(params?: Params$Resource$Projects$Instances$Tables$Restore, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        restore(params: Params$Resource$Projects$Instances$Tables$Restore, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        restore(params: Params$Resource$Projects$Instances$Tables$Restore, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        restore(params: Params$Resource$Projects$Instances$Tables$Restore, callback: BodyResponseCallback<Schema$Operation>): void;
        restore(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Sets the access control policy on a Table or Backup resource. Replaces any existing policy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Instances$Tables$Setiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setIamPolicy(params?: Params$Resource$Projects$Instances$Tables$Setiampolicy, options?: MethodOptions): GaxiosPromise<Schema$Policy>;
        setIamPolicy(params: Params$Resource$Projects$Instances$Tables$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Instances$Tables$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Instances$Tables$Setiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Returns permissions that the caller has on the specified Table or Backup resource.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Instances$Tables$Testiampermissions, options: StreamMethodOptions): GaxiosPromise<Readable>;
        testIamPermissions(params?: Params$Resource$Projects$Instances$Tables$Testiampermissions, options?: MethodOptions): GaxiosPromise<Schema$TestIamPermissionsResponse>;
        testIamPermissions(params: Params$Resource$Projects$Instances$Tables$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Instances$Tables$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Instances$Tables$Testiampermissions, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        /**
         * Restores a specified table which was accidentally deleted.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        undelete(params: Params$Resource$Projects$Instances$Tables$Undelete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        undelete(params?: Params$Resource$Projects$Instances$Tables$Undelete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        undelete(params: Params$Resource$Projects$Instances$Tables$Undelete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        undelete(params: Params$Resource$Projects$Instances$Tables$Undelete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        undelete(params: Params$Resource$Projects$Instances$Tables$Undelete, callback: BodyResponseCallback<Schema$Operation>): void;
        undelete(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Instances$Tables$Checkconsistency extends StandardParameters {
        /**
         * Required. The unique name of the Table for which to check replication consistency. Values are of the form `projects/{project\}/instances/{instance\}/tables/{table\}`.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CheckConsistencyRequest;
    }
    export interface Params$Resource$Projects$Instances$Tables$Create extends StandardParameters {
        /**
         * Required. The unique name of the instance in which to create the table. Values are of the form `projects/{project\}/instances/{instance\}`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CreateTableRequest;
    }
    export interface Params$Resource$Projects$Instances$Tables$Delete extends StandardParameters {
        /**
         * Required. The unique name of the table to be deleted. Values are of the form `projects/{project\}/instances/{instance\}/tables/{table\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Instances$Tables$Droprowrange extends StandardParameters {
        /**
         * Required. The unique name of the table on which to drop a range of rows. Values are of the form `projects/{project\}/instances/{instance\}/tables/{table\}`.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DropRowRangeRequest;
    }
    export interface Params$Resource$Projects$Instances$Tables$Generateconsistencytoken extends StandardParameters {
        /**
         * Required. The unique name of the Table for which to create a consistency token. Values are of the form `projects/{project\}/instances/{instance\}/tables/{table\}`.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GenerateConsistencyTokenRequest;
    }
    export interface Params$Resource$Projects$Instances$Tables$Get extends StandardParameters {
        /**
         * Required. The unique name of the requested table. Values are of the form `projects/{project\}/instances/{instance\}/tables/{table\}`.
         */
        name?: string;
        /**
         * The view to be applied to the returned table's fields. Defaults to `SCHEMA_VIEW` if unspecified.
         */
        view?: string;
    }
    export interface Params$Resource$Projects$Instances$Tables$Getiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Instances$Tables$List extends StandardParameters {
        /**
         * Maximum number of results per page. A page_size of zero lets the server choose the number of items to return. A page_size which is strictly positive will return at most that many items. A negative page_size will cause an error. Following the first request, subsequent paginated calls are not required to pass a page_size. If a page_size is set in subsequent calls, it must match the page_size given in the first request.
         */
        pageSize?: number;
        /**
         * The value of `next_page_token` returned by a previous call.
         */
        pageToken?: string;
        /**
         * Required. The unique name of the instance for which tables should be listed. Values are of the form `projects/{project\}/instances/{instance\}`.
         */
        parent?: string;
        /**
         * The view to be applied to the returned tables' fields. Only NAME_ONLY view (default), REPLICATION_VIEW and ENCRYPTION_VIEW are supported.
         */
        view?: string;
    }
    export interface Params$Resource$Projects$Instances$Tables$Modifycolumnfamilies extends StandardParameters {
        /**
         * Required. The unique name of the table whose families should be modified. Values are of the form `projects/{project\}/instances/{instance\}/tables/{table\}`.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ModifyColumnFamiliesRequest;
    }
    export interface Params$Resource$Projects$Instances$Tables$Patch extends StandardParameters {
        /**
         * The unique name of the table. Values are of the form `projects/{project\}/instances/{instance\}/tables/_a-zA-Z0-9*`. Views: `NAME_ONLY`, `SCHEMA_VIEW`, `REPLICATION_VIEW`, `STATS_VIEW`, `FULL`
         */
        name?: string;
        /**
         * Required. The list of fields to update. A mask specifying which fields (e.g. `change_stream_config`) in the `table` field should be updated. This mask is relative to the `table` field, not to the request message. The wildcard (*) path is currently not supported. Currently UpdateTable is only supported for the following fields: * `change_stream_config` * `change_stream_config.retention_period` * `deletion_protection` If `column_families` is set in `update_mask`, it will return an UNIMPLEMENTED error.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Table;
    }
    export interface Params$Resource$Projects$Instances$Tables$Restore extends StandardParameters {
        /**
         * Required. The name of the instance in which to create the restored table. Values are of the form `projects//instances/`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$RestoreTableRequest;
    }
    export interface Params$Resource$Projects$Instances$Tables$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Instances$Tables$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TestIamPermissionsRequest;
    }
    export interface Params$Resource$Projects$Instances$Tables$Undelete extends StandardParameters {
        /**
         * Required. The unique name of the table to be restored. Values are of the form `projects/{project\}/instances/{instance\}/tables/{table\}`.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$UndeleteTableRequest;
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Get, options?: MethodOptions): GaxiosPromise<Schema$Location>;
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$Location>, callback: BodyResponseCallback<Schema$Location>): void;
        get(params: Params$Resource$Projects$Locations$Get, callback: BodyResponseCallback<Schema$Location>): void;
        get(callback: BodyResponseCallback<Schema$Location>): void;
        /**
         * Lists information about the supported locations for this service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$List, options?: MethodOptions): GaxiosPromise<Schema$ListLocationsResponse>;
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$ListLocationsResponse>, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$List, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Get extends StandardParameters {
        /**
         * Resource name for the location.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$List extends StandardParameters {
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * The resource that owns the locations collection, if applicable.
         */
        name?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
    }
    export {};
}
