/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace verifiedaccess_v1 {
    export interface Options extends GlobalOptions {
        version: 'v1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Chrome Verified Access API
     *
     * API for Verified Access chrome extension to provide credential verification for chrome devices connecting to an enterprise network
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const verifiedaccess = google.verifiedaccess('v1');
     * ```
     */
    export class Verifiedaccess {
        context: APIRequestContext;
        challenge: Resource$Challenge;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Result message for VerifiedAccess.CreateChallenge.
     */
    export interface Schema$Challenge {
        /**
         * Challenge generated with the old signing key (this will only be present during key rotation)
         */
        alternativeChallenge?: Schema$SignedData;
        /**
         * Generated challenge
         */
        challenge?: Schema$SignedData;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * The wrapper message of any data and its signature.
     */
    export interface Schema$SignedData {
        /**
         * The data to be signed.
         */
        data?: string | null;
        /**
         * The signature of the data field.
         */
        signature?: string | null;
    }
    /**
     * signed ChallengeResponse
     */
    export interface Schema$VerifyChallengeResponseRequest {
        /**
         * The generated response to the challenge
         */
        challengeResponse?: Schema$SignedData;
        /**
         * Service can optionally provide identity information about the device or user associated with the key. For an EMK, this value is the enrolled domain. For an EUK, this value is the user's email address. If present, this value will be checked against contents of the response, and verification will fail if there is no match.
         */
        expectedIdentity?: string | null;
    }
    /**
     * Result message for VerifiedAccess.VerifyChallengeResponse.
     */
    export interface Schema$VerifyChallengeResponseResult {
        /**
         * Attested device id (ADID) of the device, read from the verified data.
         */
        attestedDeviceId?: string | null;
        /**
         * Device enrollment id is returned in this field (for the machine response only).
         */
        deviceEnrollmentId?: string | null;
        /**
         * Device permanent id is returned in this field (for the machine response only).
         */
        devicePermanentId?: string | null;
        /**
         * Certificate Signing Request (in the SPKAC format, base64 encoded) is returned in this field. This field will be set only if device has included CSR in its challenge response. (the option to include CSR is now available for both user and machine responses)
         */
        signedPublicKeyAndChallenge?: string | null;
        /**
         * For EMCert check, device permanent id is returned here. For EUCert check, signed_public_key_and_challenge [base64 encoded] is returned if present, otherwise empty string is returned. This field is deprecated, please use device_permanent_id or signed_public_key_and_challenge fields.
         */
        verificationOutput?: string | null;
    }
    export class Resource$Challenge {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * CreateChallenge API
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Challenge$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Challenge$Create, options?: MethodOptions): GaxiosPromise<Schema$Challenge>;
        create(params: Params$Resource$Challenge$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Challenge$Create, options: MethodOptions | BodyResponseCallback<Schema$Challenge>, callback: BodyResponseCallback<Schema$Challenge>): void;
        create(params: Params$Resource$Challenge$Create, callback: BodyResponseCallback<Schema$Challenge>): void;
        create(callback: BodyResponseCallback<Schema$Challenge>): void;
        /**
         * VerifyChallengeResponse API
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        verify(params: Params$Resource$Challenge$Verify, options: StreamMethodOptions): GaxiosPromise<Readable>;
        verify(params?: Params$Resource$Challenge$Verify, options?: MethodOptions): GaxiosPromise<Schema$VerifyChallengeResponseResult>;
        verify(params: Params$Resource$Challenge$Verify, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        verify(params: Params$Resource$Challenge$Verify, options: MethodOptions | BodyResponseCallback<Schema$VerifyChallengeResponseResult>, callback: BodyResponseCallback<Schema$VerifyChallengeResponseResult>): void;
        verify(params: Params$Resource$Challenge$Verify, callback: BodyResponseCallback<Schema$VerifyChallengeResponseResult>): void;
        verify(callback: BodyResponseCallback<Schema$VerifyChallengeResponseResult>): void;
    }
    export interface Params$Resource$Challenge$Create extends StandardParameters {
        /**
         * Request body metadata
         */
        requestBody?: Schema$Empty;
    }
    export interface Params$Resource$Challenge$Verify extends StandardParameters {
        /**
         * Request body metadata
         */
        requestBody?: Schema$VerifyChallengeResponseRequest;
    }
    export {};
}
