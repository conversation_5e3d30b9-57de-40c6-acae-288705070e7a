// File test để kiểm tra parser với HTML mẫu
const emailParser = require('./src/services/emailParser');

// HTML mẫu từ email CAKE
const sampleHTML = `
<div class="m_4253994842280454992mj-column-per-100" style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%">
  <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%">
    <tbody>
      <tr>
        <td style="vertical-align:top;padding:0 0 12px">
          <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%">
            <tbody>
              <tr>
                <td align="left" style="font-size:0px;padding:12px 16px;word-break:break-word">
                  <div style="font-family:Be Vietnam Pro 2022,sans-serif;font-size:15px;font-weight:bold;line-height:20px;text-align:left;color:#394860">Thông tin tài khoản</div>
                </td>
              </tr>
              <tr>
                <td align="left" style="font-size:0px;padding:0;word-break:break-word">
                  <table cellpadding="0" cellspacing="0" width="100%" border="0" style="color:#666f80;font-family:Be Vietnam Pro 2022,sans-serif;font-size:14px;line-height:20px;table-layout:auto;width:100%;border:none">
                    <tbody>
                      <tr>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td style="width:30%;padding:8px 0;background-color:#ffffff;border-bottom:1px solid #f2f1f7"> Tài khoản nhận </td>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td width="1px"></td>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td style="padding:8px 0;background-color:#ffffff;border-bottom:1px solid #f2f1f7;color:#071a38">
                          0916496246 - Tài khoản thanh toán
                        </td>
                        <td style="width:16px;background-color:#ffffff"></td>
                      </tr>
                      <tr>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td style="width:30%;padding:8px 0;background-color:#ffffff;border-bottom:1px solid #f2f1f7"> Tài khoản chuyển </td>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td width="1px"></td>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td style="padding:8px 0;background-color:#ffffff;border-bottom:1px solid #f2f1f7;color:#071a38">
                          0916496246
                        </td>
                        <td style="width:16px;background-color:#ffffff"></td>
                      </tr>
                      <tr>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td style="width:30%;padding:8px 0;background-color:#ffffff;border-bottom:1px solid #f2f1f7"> Tên người chuyển </td>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td width="1px"></td>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td style="padding:8px 0;background-color:#ffffff;border-bottom:1px solid #f2f1f7;color:#071a38">
                          MBBANK IBFT
                        </td>
                        <td style="width:16px;background-color:#ffffff"></td>
                      </tr>
                      <tr>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td style="width:30%;padding:8px 0;background-color:#ffffff"> Ngân hàng chuyển </td>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td width="1px"></td>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td style="padding:8px 0;background-color:#ffffff;color:#071a38">
                          MBB
                        </td>
                        <td style="width:16px;background-color:#ffffff"></td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
        </td>
      </tr>
    </tbody>
  </table>
</div>
<div class="m_4253994842280454992mj-column-per-100" style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%">
  <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%">
    <tbody>
      <tr>
        <td style="vertical-align:top;padding:0 0 12px">
          <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%">
            <tbody>
              <tr>
                <td align="left" style="font-size:0px;padding:12px 16px;word-break:break-word">
                  <div style="font-family:Be Vietnam Pro 2022,sans-serif;font-size:15px;font-weight:bold;line-height:20px;text-align:left;color:#394860">Thông tin giao dịch</div>
                </td>
              </tr>
              <tr>
                <td align="left" style="font-size:0px;padding:0;word-break:break-word">
                  <table cellpadding="0" cellspacing="0" width="100%" border="0" style="color:#666f80;font-family:Be Vietnam Pro 2022,sans-serif;font-size:14px;line-height:20px;table-layout:auto;width:100%;border:none">
                    <tbody>
                      <tr>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td style="width:30%;padding:8px 0;background-color:#ffffff;border-bottom:1px solid #f2f1f7"> Loại giao dịch </td>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td width="1px"></td>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td style="padding:8px 0;background-color:#ffffff;border-bottom:1px solid #f2f1f7;color:#071a38">
                          Chuyển tiền ngoài CAKE
                        </td>
                        <td style="width:16px;background-color:#ffffff"></td>
                      </tr>
                      <tr>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td style="width:30%;padding:8px 0;background-color:#ffffff;border-bottom:1px solid #f2f1f7"> Mã giao dịch </td>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td width="1px"></td>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td style="padding:8px 0;background-color:#ffffff;border-bottom:1px solid #f2f1f7;color:#071a38">
                          297247827
                        </td>
                        <td style="width:16px;background-color:#ffffff"></td>
                      </tr>
                      <tr>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td style="width:30%;padding:8px 0;background-color:#ffffff;border-bottom:1px solid #f2f1f7"> Ngày giờ giao dịch </td>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td width="1px"></td>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td style="padding:8px 0;background-color:#ffffff;border-bottom:1px solid #f2f1f7;color:#071a38">
                          06/08/2025, 01:50:59
                        </td>
                        <td style="width:16px;background-color:#ffffff"></td>
                      </tr>
                      <tr>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td style="width:30%;padding:8px 0;background-color:#ffffff;border-bottom:1px solid #f2f1f7"> Số tiền </td>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td width="1px"></td>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td style="padding:8px 0;background-color:#ffffff;border-bottom:1px solid #f2f1f7;color:#188126"> +5.000 đ</td>
                        <td style="width:16px;background-color:#ffffff"></td>
                      </tr>
                      <tr>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td style="width:30%;padding:8px 0;background-color:#ffffff;border-bottom:1px solid #f2f1f7"> Phí giao dịch </td>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td width="1px"></td>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td style="padding:8px 0;background-color:#ffffff;border-bottom:1px solid #f2f1f7;color:#071a38"> 0 đ </td>
                        <td style="width:16px;background-color:#ffffff"></td>
                      </tr>
                      <tr>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td style="width:30%;padding:8px 0;background-color:#ffffff"> Nội dung giao dịch </td>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td width="1px"></td>
                        <td style="width:16px;background-color:#ffffff"></td>
                        <td style="padding:8px 0;background-color:#ffffff;color:#071a38">
                          LE ANH DUY chuyen tien
                        </td>
                        <td style="width:16px;background-color:#ffffff"></td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
        </td>
      </tr>
    </tbody>
  </table>
</div>
`;

console.log('Testing email parser with sample HTML...');
console.log('='.repeat(50));

const result = emailParser.parseTransactionEmail(sampleHTML);

if (result) {
  console.log('✅ Parser thành công!');
  console.log('Dữ liệu đã trích xuất:');
  console.log(JSON.stringify(result, null, 2));

  // Test riêng parse datetime
  console.log('\n🔍 Test parse datetime:');
  console.log('Input:', result.ngayGioGiaoDich);
  console.log('Parsed:', result.ngayGioGiaoDichDate);
  console.log('Local time:', result.ngayGioGiaoDichDate.toLocaleString('vi-VN', { timeZone: 'Asia/Ho_Chi_Minh' }));
  console.log('Expected: 2025-08-06 01:50:59');

  // Test manual parse
  const testParser = require('./src/services/emailParser');
  const manualTest = testParser.parseDateTime('06/08/2025, 01:50:59');
  console.log('Manual test:', manualTest);
  console.log('Manual local:', manualTest.toLocaleString('vi-VN', { timeZone: 'Asia/Ho_Chi_Minh' }));
} else {
  console.log('❌ Parser thất bại!');
}

console.log('='.repeat(50));
